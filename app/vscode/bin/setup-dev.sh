#!/bin/sh

echo "Setting up development environment..."

# 确保 dev 目录存在
mkdir -p dev

# 复制或链接资源文件（保持软链接用于开发便利）
echo "Setting up asset links..."
[ ! -e "dev/media" ] && ln -sf ../asset/media dev/media
[ ! -e "dev/walkthrough" ] && ln -sf ../asset/walkthrough dev/walkthrough
[ ! -e "dev/package.json" ] && ln -sf ../asset/package.json dev/package.json
[ ! -e "dev/template" ] && ln -sf ../../../template dev/template

# 设置构建产物链接
echo "Setting up build artifact links..."
mkdir -p dev/extension dev/webview
[ ! -e "dev/extension/dist" ] && ln -sf ../../../../lib/extension/dist dev/extension/dist
[ ! -e "dev/webview/asset" ] && ln -sf ../../../../lib/webview/asset dev/webview/asset
[ ! -e "dev/webview/dist" ] && ln -sf ../../../../lib/webview/dist dev/webview/dist

echo "Development environment setup complete."
