# AI Chat

1. You can start a chat using one of the following options:
   1. Run the `CodeBeat: Start Chat 💬` command from the command palette.
   1. Select the `Start Chat 💬` entry in the editor context menu (right-click, requires selection).
   1. Use the "Start new chat" button in the side panel.
   1. Use the keyboard shortcut: `Ctrl + Cmd + C` (Mac) or `Ctrl + Alt + C` (Windows / Linux).
   1. Press 💬 on the MacOS touch bar (if available).
2. Ask a question in the new conversation thread in the CodeBeat sidebar panel. CodeBeat knows the editor selection at the time of conversation start.

![AI Chat](https://raw.githubusercontent.com/our-aicorp/codebeat-vscode/main/app/vscode/asset/media/screenshot-start-chat.png)
