# CodeBeat Templates

What if you want to craft an AI Chat that knows _specifically_ about your conventions?
How cool would it be to have the answers in your own language?

You can craft your own conversation templates by adding `.cbt.md` files to the `.codebeat/template` folder in your workspace. See the [CodeBeat Template docs](https://github.com/our-aicorp/codebeat-vscode/blob/main/doc/codebeat-templates.md) for more information.

To use custom conversations, run the "CodeBeat: Start Custom Chat… 💬" command.

Here is an example of a [function call graph generator](https://github.com/our-aicorp/codebeat-vscode/blob/main/template/fun/function-call-graph.cbt.md):

![Generate function call graph](https://raw.githubusercontent.com/our-aicorp/codebeat-vscode/main/app/vscode/asset/media/function-call-graph.gif)

[Learn how to craft your own CodeBeat Template](https://github.com/our-aicorp/codebeat-vscode/blob/main/doc/codebeat-templates.md)!
