# Other Actions

CodeBeat supports many commands that can be accessed from the command palette, the custom chat command, and from the editor context menu:

- 🪄 **Generate Unit Tests** _(requires editor selection)_
- 💬 **Explain Code** _(requires editor selection)_
- 🐛 **Finds Bugs** _(requires editor selection)_
- 🔎 **Diagnose Errors** _(requires editor selection with errors or warnings)_
- 📝 **Document Code** _(requires editor selection)_
- 👓 **Improve Readability** _(requires editor selection)_

![Diagnose Errors](https://raw.githubusercontent.com/our-aicorp/codebeat-vscode/main/app/vscode/asset/media/screenshot-diagnose-errors.gif)
