# Generate Code

1. You can start generating code using one of the following options:
   1. Run the `CodeBeat: Generate Code 💬` command from the command palette.
   1. Use the "Generate Code" toolbar button in the side panel.
   1. Use the keyboard shortcut: `Ctrl + Cmd + G` (Mac) or `Ctrl + Alt + G` (Windows / Linux).
2. Describe what you want to generate in the new conversation thread in the CodeBeat sidebar panel. CodeBeat will generate code for you based on your description. Further messages can be used to refine the generated code.

![Generate Code](https://raw.githubusercontent.com/our-aicorp/codebeat-vscode/main/app/vscode/asset/media/screenshot-generate-code.gif)

> 💡&nbsp;&nbsp;You can create your own customized code generators with [CodeBeat Templates](https://github.com/our-aicorp/codebeat-vscode/blob/main/doc/codebeat-templates.md).
