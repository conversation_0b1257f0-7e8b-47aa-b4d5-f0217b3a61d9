# Edit Code

1. Select the code that you want to change in the editor.
2. Invoke the "Edit Code" command using one of the following options:
   1. Run the `CodeBeat: Edit Code 💬` command from the command palette.
   1. Use the "Edit Code" toolbar button in the side panel.
   1. Select the `Edit Code 💬` entry in the editor context menu (right-click).
   1. Use the keyboard shortcut: `Ctrl + Cmd + E` (Mac) or `Ctrl + Alt + E` (Windows / Linux).
3. CodeBeat will generate a diff view.
4. Provide additional instructions to Code<PERSON><PERSON> in the chat thread.
5. When you are happy with the changes, apply them using the "Apply" button in the diff view.

![Edit Code](https://raw.githubusercontent.com/our-aicorp/codebeat-vscode/main/app/vscode/asset/media/screenshot-edit-code.gif)
