{"name": "codebeat-vscode", "version": "1.21.0", "displayName": "CodeBeat", "description": "Generate code, edit code, explain code, create test cases, identify bugs, diagnose errors, and even craft conversation templates with a personal style.", "keywords": ["ai", "ai chat", "ai assistant", "code generation", "code enhancement", "code explanation", "code optimization", "code improvement", "code readability", "code diff", "code review", "code auditing", "test case generation", "test case creation", "bug identification", "error diagnosis", "documentation", "custom prompt", "custom conversations", "conversation templates", "openai", "chatgpt", "gpt", "codex", "copilot", "llm", "ollama"], "categories": ["Programming Languages", "Snippets", "Linters", "Education", "Machine Learning", "Data Science", "Testing", "AI", "Cha<PERSON>", "Formatters", "Debuggers"], "icon": "media/extension-icon.png", "galleryBanner": {"color": "#000000", "theme": "dark"}, "publisher": "AICorp", "license": "SEE LICENSE IN LICENSE.txt", "pricing": "Free", "engines": {"node": ">=18", "vscode": "^1.72.0"}, "homepage": "https://github.com/our-aicorp/codebeat-vscode", "repository": "https://github.com/our-aicorp/codebeat-vscode", "bugs": "https://github.com/our-aicorp/codebeat-vscode/issues", "extensionKind": ["workspace"], "main": "./extension/dist/extension.js", "activationEvents": ["onStartupFinished"], "contributes": {"commands": [{"command": "codebeat.enterOpenAIApiKey", "category": "CodeBeat", "title": "Enter OpenAI API key"}, {"command": "codebeat.clearOpenAIApiKey", "category": "CodeBeat", "title": "Clear OpenAI API key"}, {"command": "codebeat.startChat", "title": "Start Chat 💬", "category": "CodeBeat", "icon": "$(comment-discussion)"}, {"command": "codebeat.editCode", "title": "Edit Code 🖊️", "category": "CodeBeat", "icon": "$(edit)"}, {"command": "codebeat.explainCode", "title": "Explain Code 💬", "category": "CodeBeat", "icon": "$(comment-discussion)"}, {"command": "codebeat.generateCode", "title": "Generate Code 🪄", "category": "CodeBeat", "icon": "$(wand)"}, {"command": "codebeat.generateUnitTest", "title": "Generate Unit Test 🪄", "category": "CodeBeat", "icon": "$(beaker)"}, {"command": "codebeat.diagnoseErrors", "title": "Diagnose Errors 💬", "category": "CodeBeat", "icon": "$(search-fuzzy)"}, {"command": "codebeat.findBugs", "title": "Find Bugs 💬", "category": "CodeBeat", "icon": "$(bug)"}, {"command": "codebeat.startCustomChat", "title": "Start Custom Chat… 💬", "category": "CodeBeat", "icon": "$(comment-draft)"}, {"command": "codebeat.touchBar.startChat", "category": "CodeBeat", "title": "💬"}, {"command": "codebeat.showChatPanel", "title": "Show Chat Panel 💬", "category": "CodeBeat", "icon": "$(comment-discussion)"}, {"command": "codebeat.getStarted", "title": "Get Started", "category": "CodeBeat", "icon": "$(question)"}, {"command": "codebeat.reloadTemplates", "title": "Reload Templates", "category": "CodeBeat", "icon": "$(sync)"}, {"command": "codebeat.showLogs", "title": "Show Logs", "category": "CodeBeat", "icon": "$(output)"}, {"command": "codebeat.indexRepository", "title": "Index Repository", "category": "CodeBeat", "enablement": "config.codebeat.indexRepository.enabled"}], "configuration": {"title": "CodeBeat", "properties": {"codebeat.syntaxHighlighting.useVisualStudioCodeColors": {"type": "boolean", "default": false, "markdownDescription": "Use the Visual Studio Code Theme colors for syntax highlighting in the diff viewer. Might not work with all themes. Only applies to newly opened diffs.", "scope": "application"}, "codebeat.indexRepository.enabled": {"type": "boolean", "default": false, "markdownDescription": "Enable the command to index your repository and give more context to the AI model. Experimental.", "scope": "application"}, "codebeat.action.editCode.showInEditorContextMenu": {"type": "boolean", "default": true, "markdownDescription": "Show this action in the editor context menu, when you right-click on the code.", "scope": "application"}, "codebeat.action.startChat.showInEditorContextMenu": {"type": "boolean", "default": true, "markdownDescription": "Show this action in the editor context menu, when you right-click on the code.", "scope": "application"}, "codebeat.action.explainCode.showInEditorContextMenu": {"type": "boolean", "default": true, "markdownDescription": "Show this action in the editor context menu, when you right-click on the code.", "scope": "application"}, "codebeat.action.findBugs.showInEditorContextMenu": {"type": "boolean", "default": true, "markdownDescription": "Show this action in the editor context menu, when you right-click on the code.", "scope": "application"}, "codebeat.action.generateUnitTest.showInEditorContextMenu": {"type": "boolean", "default": true, "markdownDescription": "Show this action in the editor context menu, when you right-click on the code.", "scope": "application"}, "codebeat.action.diagnoseErrors.showInEditorContextMenu": {"type": "boolean", "default": true, "markdownDescription": "Show this action in the editor context menu, when you right-click on the code.", "scope": "application"}, "codebeat.action.startCustomChat.showInEditorContextMenu": {"type": "boolean", "default": true, "markdownDescription": "Show this action in the editor context menu, when you right-click on the code.", "scope": "application"}, "codebeat.logger.level": {"type": "string", "default": "info", "enum": ["debug", "info", "warning", "error"], "enumDescriptions": ["Show all logs", "Show all logs except the debug ones", "Only show warnings and errors", "Only show errors"], "markdownDescription": "Specify the verbosity of logs that will appear in 'CodeBeat: Show Logs'.", "scope": "application"}, "codebeat.openAI.surfacePromptForPlus": {"type": "boolean", "default": false, "markdownDescription": "Enable UI to surface the prompt text to use with OpenAI plus web chat", "scope": "application"}, "codebeat.openAI.baseUrl": {"type": "string", "default": "https://api.openai.com/v1/", "markdownDescription": "Specify the URL to the OpenAI API. If you are using a proxy, you can set it here.", "scope": "application"}, "codebeat.ollama.baseUrl": {"type": "string", "default": "http://localhost:11434", "markdownDescription": "Specify the URL to the Ollama API. Default is the local Ollama server.", "scope": "application"}, "codebeat.env": {"OLLAMA_FLASH_ATTENTION": "true", "OLLAMA_NUM_PARALLEL": "8", "CUDA_VISIBLE_DEVICES": "0", "OLLAMA_KEEP_ALIVE": "5m0s"}, "codebeat.model": {"type": "string", "default": "deepseek-r1:1.5b", "enum": ["deepseek-r1:1.5b", "llama3.1", "mistral", "codellama", "gpt-3.5-turbo", "gpt-3.5-turbo-16k", "gpt-3.5-turbo-1106", "gpt-3.5-turbo-0125", "gpt-4", "gpt-4-32k", "gpt-4-1106-preview", "gpt-4-0125-preview", "gpt-4-turbo-preview", "gpt-4o", "gpt-4o-mini", "llama.cpp"], "enumDescriptions": ["Ollama deepseek-r1 1.5b: Fast, local model. Free to use. Requires Ollama server running locally.", "Ollama Llama 3: Fast, local model. Free to use. Requires Ollama server running locally.", "Ollama Llama 3.1: Latest Llama model. Fast, local model. Free to use. Requires Ollama server running locally.", "Ollama Mistral: Fast, local model. Good for code generation. Free to use. Requires Ollama server running locally.", "Ollama CodeLlama: Specialized for code generation. Fast, local model. Free to use. Requires Ollama server running locally.", "OpenAI GPT-3.5-turbo: 4k context window. Faster, less expensive model. Less accurate.", "OpenAI GPT-3.5-turbo: 16k context window. Faster, less expensive model. Less accurate.", "OpenAI GPT-3.5-turbo: 16k context window. Faster, less expensive. Less accurate.", "OpenAI GPT-3.5-turbo: 16k context window. Faster, less expensive. Less accurate. The latest GPT-3.5 Turbo model with higher accuracy at responding in requested formats and a fix for a bug which caused a text encoding issue for non-English language function calls. Returns a maximum of 4,096 output tokens.", "OpenAI GPT-4: 8k context window. Expensive, slow model. More accurate.", "OpenAI GPT-4: 32k context window. Expensive, slow model. More accurate.", "OpenAI GPT-4 Turbo: 128k context window. Expensive (but cheaper than 32k), slow model. More accurate.", "OpenAI GPT-4 Turbo: 128k context window. Expensive (but cheaper than 32k), slow model. More accurate. The latest GPT-4 model intended to reduce cases of \"laziness\" where the model doesn't complete a task.", "OpenAI GPT-4 Turbo: Currently points to gpt-4-0125-preview.", "OpenAI GPT-4o: Latest multimodal model. Fast and capable.", "OpenAI GPT-4o-mini: Smaller, faster version of GPT-4o.", "(Deprecated) Llama.cpp: Use Ollama models instead. Maps to llama3."], "markdownDescription": "Select the AI model that you want to use. Ollama models run locally and are free, while OpenAI models require an API key.", "scope": "application"}}}, "keybindings": [{"command": "codebeat.startChat", "when": "isMac", "key": "Ctrl+Cmd+c"}, {"command": "codebeat.startChat", "when": "!isMac", "key": "Ctrl+Alt+c"}, {"command": "codebeat.editCode", "when": "isMac", "key": "Ctrl+Cmd+e"}, {"command": "codebeat.editCode", "when": "!isMac", "key": "Ctrl+Alt+e"}, {"command": "codebeat.generateCode", "when": "isMac", "key": "Ctrl+Cmd+g"}, {"command": "codebeat.generateCode", "when": "!isMac", "key": "Ctrl+Alt+g"}], "menus": {"view/title": [{"command": "codebeat.startChat", "when": "view == codebeat.chat", "group": "navigation@1"}, {"command": "codebeat.editCode", "when": "view == codebeat.chat", "group": "navigation@2"}, {"command": "codebeat.generateCode", "when": "view == codebeat.chat", "group": "navigation@3"}, {"command": "codebeat.startCustomChat", "when": "view == codebeat.chat", "group": "navigation@6"}, {"command": "codebeat.getStarted", "when": "view == codebeat.chat", "group": "navigation@9"}], "touchBar": [{"command": "codebeat.touchBar.startChat", "group": "codebeat"}], "commandPalette": [{"command": "codebeat.touchBar.startChat", "when": "false"}], "editor/context": [{"command": "codebeat.startChat", "group": "codebeat", "when": "config.codebeat.action.startChat.showInEditorContextMenu && editorHasSelection"}, {"command": "codebeat.editCode", "group": "codebeat", "when": "config.codebeat.action.editCode.showInEditorContextMenu && editorHasSelection"}, {"command": "codebeat.explainCode", "group": "codebeat", "when": "config.codebeat.action.explainCode.showInEditorContextMenu && editorHasSelection"}, {"command": "codebeat.findBugs", "group": "codebeat", "when": "config.codebeat.action.findBugs.showInEditorContextMenu && editorHasSelection"}, {"command": "codebeat.generateUnitTest", "group": "codebeat", "when": "config.codebeat.action.generateUnitTest.showInEditorContextMenu && editorHasSelection"}, {"command": "codebeat.diagnoseErrors", "group": "codebeat", "when": "config.codebeat.action.diagnoseErrors.showInEditorContextMenu && editorHasSelection"}, {"command": "codebeat.startCustomChat", "group": "codebeat", "when": "config.codebeat.action.startCustomChat.showInEditorContextMenu && editorHasSelection"}]}, "viewsContainers": {"activitybar": [{"id": "codebeat", "title": "CodeBeat", "icon": "media/sidebar-icon.svg"}]}, "views": {"codebeat": [{"id": "codebeat.chat", "name": "Cha<PERSON>", "type": "webview"}]}, "walkthroughs": [{"id": "codebeat", "title": "CodeBeat", "description": "Your AI Chat Assistant in Visual Studio Code", "steps": [{"id": "setup", "title": "Setup CodeBeat", "description": "[Enter your OpenAI API key](command:codebeat.enterOpenAIApiKey) to get started.", "media": {"markdown": "walkthrough/setup.md"}}, {"id": "chat", "title": "AI Chat", "description": "Chat with CodeBeat about your code and software development topics.", "media": {"markdown": "walkthrough/chat.md"}}, {"id": "generate-code", "title": "Generate Code", "description": "Generate code by instructing CodeBeat.", "media": {"markdown": "walkthrough/generate-code.md"}}, {"id": "edit-code", "title": "Edit Code", "description": "Change the selected code by instructing CodeBeat to create an edit.", "media": {"markdown": "walkthrough/edit-code.md"}}, {"id": "other-actions", "title": "Other Actions", "description": "Generate unit tests, explain code, find bugs, and more.", "media": {"markdown": "walkthrough/other-actions.md"}}, {"id": "codebeat-templates", "title": "CodeBeat Templates", "description": "Craft your own chat templates!", "media": {"markdown": "walkthrough/codebeat-templates.md"}}, {"id": "tips-and-tricks", "title": "Tips and Tricks", "description": "How to get the most out of CodeBeat.", "media": {"markdown": "walkthrough/tips-and-tricks.md"}}, {"id": "project", "title": "Project", "description": "Learn more about the CodeBeat open source project.", "media": {"markdown": "walkthrough/project.md"}}]}]}}