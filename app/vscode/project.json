{"root": "app/vscode", "targets": {"build": {"dependsOn": ["^build"], "executor": "nx:noop"}, "dev-setup": {"dependsOn": ["build"], "executor": "nx:run-commands", "options": {"cwd": "app/vscode", "command": "bin/setup-dev.sh"}}, "package": {"dependsOn": ["build"], "executor": "nx:run-commands", "options": {"cwd": "app/vscode", "command": "bin/package.sh"}}, "publish-vscode": {"dependsOn": ["package"], "executor": "nx:run-commands", "options": {"cwd": "app/vscode", "command": "pnpm vsce publish -i `ls dist/*.vsix`"}}, "publish-ovsx": {"dependsOn": ["package"], "executor": "nx:run-commands", "options": {"cwd": "app/vscode", "command": "pnpm ovsx publish -i `ls dist/*.vsix`"}}}}