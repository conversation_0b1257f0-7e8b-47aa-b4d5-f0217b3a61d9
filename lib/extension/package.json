{"name": "@codebeat/extension", "version": "0.0.0", "private": true, "scripts": {"test": "vitest run src/", "test-watch": "vitest src/"}, "devDependencies": {"@types/handlebars": "4.1.0", "@types/marked": "4.0.8", "@types/vscode": "1.72.0"}, "dependencies": {"@langchain/core": "^0.3.61", "@langchain/ollama": "^0.2.3", "@langchain/openai": "^0.5.15", "@codebeat/common": "workspace:*", "handlebars": "4.7.8", "marked": "4.2.12", "secure-json-parse": "2.7.0", "simple-git": "3.21.0", "zod": "^3.25.67"}}