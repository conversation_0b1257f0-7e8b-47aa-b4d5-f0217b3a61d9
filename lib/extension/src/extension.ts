import * as vscode from "vscode";
import { AIClient } from "./ai/AIClient";
import { APIKeyManager } from "./ai/ApiKeyManager";
import { ChatController } from "./chat/ChatController";
import { ChatModel } from "./chat/ChatModel";
import { ChatPanel } from "./chat/ChatPanel";
import { WorkspaceChatPersistenceManager } from "./chat/ChatPersistenceManager";
import { ConversationTypesProvider } from "./conversation/ConversationTypesProvider";
import { DiffEditorManager } from "./diff/DiffEditorManager";
import { indexRepository } from "./index/indexRepository";
import { getVSCodeLogLevel, LoggerUsingVSCodeOutput } from "./logger";
import { ErrorHandler } from "./utils/ErrorHandler";
import { PerformanceMonitor } from "./utils/PerformanceMonitor";

export const activate = async (context: vscode.ExtensionContext) => {
  console.log("🚀 CodeBeat extension activation started");

  try {
    const mainOutputChannel = vscode.window.createOutputChannel("CodeBeat");
    const indexOutputChannel =
      vscode.window.createOutputChannel("CodeBeat Index");

    // Initialize utilities
    ErrorHandler.initialize(mainOutputChannel);
    PerformanceMonitor.initialize(mainOutputChannel);

    console.log("✅ CodeBeat utilities initialized");

    const vscodeLogger = new LoggerUsingVSCodeOutput({
      outputChannel: mainOutputChannel,
      level: getVSCodeLogLevel(),
    });

    vscode.workspace.onDidChangeConfiguration((event) => {
      if (event.affectsConfiguration("codebeat.logger.level")) {
        vscodeLogger.setLevel(getVSCodeLogLevel());
      }
    });
    console.log("✅ VSCode logger initialized");

    const apiKeyManager = new APIKeyManager({
      secretStorage: context.secrets,
    });
    console.log("✅ API key manager created");

    const hasOpenAIApiKey = await apiKeyManager.hasOpenAIApiKey();
    console.log("✅ OpenAI API key status checked:", { hasOpenAIApiKey });

    const chatPanel = new ChatPanel({
      extensionUri: context.extensionUri,
      apiKeyManager,
      hasOpenAIApiKey,
    });
    console.log("✅ Chat panel created");

    const chatModel = new ChatModel();
    console.log("✅ Chat model created");

    const conversationTypesProvider = new ConversationTypesProvider({
      extensionUri: context.extensionUri,
    });
    await conversationTypesProvider.loadConversationTypes();
    console.log("✅ Conversation types loaded");

    const ai = new AIClient({
      apiKeyManager,
      logger: vscodeLogger,
    });
    console.log("✅ LLM client created");

    const diffEditorManager = new DiffEditorManager({
      extensionUri: context.extensionUri,
    });
    console.log("✅ Diff editor manager created");

    const chatController = new ChatController({
      chatPanel,
      chatModel,
      ai,
      diffEditorManager,
      getConversationType(id: string) {
        return conversationTypesProvider.getConversationType(id);
      },
      // 设置默认对话模板, 用于新建对话框
      // TODO: retrieve from setting or automaic detection from language mode
      basicChatTemplateId: "chat-en",
    });
    console.log("✅ Chat controller created");

    // Initialize workspace chat persistence manager
    const persistenceManager = new WorkspaceChatPersistenceManager(
      context,
      (id: string) => conversationTypesProvider.getConversationType(id),
      ai,
      diffEditorManager,
      () => chatController.updateChatPanel()
    );
    console.log("✅ Worksapce Chat Persistence manager created");

    chatModel.setPersistenceManager(persistenceManager);
    console.log("✅ Chat model bound to chat persistence manager");

    // Load saved conversations
    await chatModel.loadState();
    await chatController.updateChatPanel();
    console.log("✅ Chat state loaded from Chat model and Chat panel updated");

    chatPanel.onDidReceiveMessage(
      chatController.receivePanelMessage.bind(chatController)
    );
    console.log("✅ Chat panl bound to Chat controller(message handlers connected)");

    context.subscriptions.push(
      //注册一个 Webview 视图（codebeat.chat），用于显示聊天面板。
      vscode.window.registerWebviewViewProvider(
        "codebeat.chat",
        chatPanel
      ),
      //注册OpenAI API key 管理命令
      vscode.commands.registerCommand(
        "codebeat.enterOpenAIApiKey",
        apiKeyManager.enterOpenAIApiKey.bind(apiKeyManager)
      ),
      //注册OpenAI API key 清除命令
      vscode.commands.registerCommand(
        "codebeat.clearOpenAIApiKey",
        async () => {
          await apiKeyManager.clearOpenAIApiKey();
          vscode.window.showInformationMessage("OpenAI API key cleared.");
        }
      ),
      //注册启动指定对话模板会话命令
      vscode.commands.registerCommand(
        "codebeat.startConversation",
        (templateId) => {
          console.log("🎯 codebeat.startConversation command invoked. Template ID: %s", templateId);
          chatController.createConversation(templateId);
        }
      ),
      //注册新建对话命令
      vscode.commands.registerCommand("codebeat.startChat", () => {
        console.log("🎯 codebeat.startChat command invoked");
        return chatController.createConversationSafe("chat-en");
      }),
      ///注册新建对话命令, 与Macbook触控栏集成 
      vscode.commands.registerCommand("codebeat.touchBar.startChat", () => {
        return chatController.createConversationSafe("chat-en");
      }),
      //注册代码错误诊断启动命令
      vscode.commands.registerCommand("codebeat.diagnoseErrors", () => {
        console.log("🎯 codebeat.diagnoseErrors command invoked");
        return chatController.createConversationSafe("diagnose-errors");
      }),
      //注册代码解析启动命令
      vscode.commands.registerCommand("codebeat.explainCode", () => {
        console.log("🎯 codebeat.explainCode command invoked");
        return chatController.createConversationSafe("explain-code");
      }),
      //注册代码缺陷检查启动命令
      vscode.commands.registerCommand("codebeat.findBugs", () => {
        console.log("🎯 codebeat.findBugs command invoked");
        return chatController.createConversationSafe("find-bugs");
      }),
      //注册代码生成对话启动命令
      vscode.commands.registerCommand("codebeat.generateCode", () => {
        console.log("🎯 codebeat.generateCode command invoked");
        return chatController.createConversationSafe("generate-code");
      }),
      //注册单元测试生成对话启动命令
      vscode.commands.registerCommand("codebeat.generateUnitTest", () => {
        console.log("🎯 codebeat.generateUnitTest command invoked");
        return chatController.createConversationSafe("generate-unit-test");
      }),
      //注册AI代码编辑启动命令
      vscode.commands.registerCommand("codebeat.editCode", () => {
        console.log("🎯 codebeat.editCode command invoked");
        chatController.createConversation("edit-code");
      }),
      //注册显示当前VSCode扩展运行性能报告启动命令
      vscode.commands.registerCommand("codebeat.showPerformanceReport", () => {
        PerformanceMonitor.showPerformanceReport();
        console.log("🎯 codebeat.showPerformanceReport command executed");
      }),
      //注册自定义对话启动命令
      vscode.commands.registerCommand("codebeat.startCustomChat", async () => {
        console.log("🎯 codebeat.startCustomChat command invoked");
        const items = conversationTypesProvider
          .getConversationTypes()
          .map((conversationType) => ({
            id: conversationType.id,
            label: conversationType.label,
            description: (() => {
              const tags = conversationType.tags;
              return tags == null
                ? conversationType.source
                : `${conversationType.source}, ${tags.join(", ")}`;
            })(),
            detail: conversationType.description,
          }));

        const result = await vscode.window.showQuickPick(items, {
          title: `Start Custom Chat…`,
          matchOnDescription: true,
          matchOnDetail: true,
          placeHolder: "Select conversation type…",
        });

        if (result == undefined) {
          return; // user cancelled
        }
        console.log(
          "🎯 codebeat.startCustomChat command resolved. Conversation type ID: %s",
          result.id
        );
        await chatController.createConversationSafe(result.id);
      }),
      
      vscode.commands.registerCommand("codebeat.showChatPanel", async () => {
        await chatController.showChatPanel();
      }),
      vscode.commands.registerCommand("codebeat.getStarted", async () => {
        await vscode.commands.executeCommand("workbench.action.openWalkthrough", {
          category: `codebeat.codebeat-vscode#codebeat`,
        });
      }),
      vscode.commands.registerCommand("codebeat.reloadTemplates", async () => {
        await conversationTypesProvider.loadConversationTypes();
        vscode.window.showInformationMessage("CodeBeat templates reloaded.");
      }),

      vscode.commands.registerCommand("codebeat.showLogs", () => {
        mainOutputChannel.show(true);
      }),

      vscode.commands.registerCommand("codebeat.indexRepository", () => {
        indexRepository({
          ai: ai,
          outputChannel: indexOutputChannel,
        });
      }),

      vscode.commands.registerCommand("codebeat.clearConversationHistory", async () => {
        const result = await vscode.window.showWarningMessage(
          "Are you sure you want to clear all conversation history? This action cannot be undone.",
          { modal: true },
          "Clear History"
        );

        if (result === "Clear History") {
          await persistenceManager.clearConversations();
          chatModel.conversations = [];
          chatModel.selectedConversationId = undefined;
          await chatController.updateChatPanel();
          vscode.window.showInformationMessage("Conversation history cleared.");
        }
      })
    );

    console.log("✅ All commands registered successfully");
    console.log("🎉 CodeBeat extension activation completed successfully!");

    return Object.freeze({
      async registerTemplate({ template }: { template: string }) {
        conversationTypesProvider.registerExtensionTemplate({ template });
        await conversationTypesProvider.loadConversationTypes();
      },
    });
  } catch (error) {
    console.error("❌ CodeBeat extension activation failed:", error);
    vscode.window.showErrorMessage(
      `CodeBeat extension failed to activate: ${error instanceof Error ? error.message : String(error)}`
    );
    throw error;
  }
};

export const deactivate = async () => {
  console.log("👋 CodeBeat extension deactivated");
};
