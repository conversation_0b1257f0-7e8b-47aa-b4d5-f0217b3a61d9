import { Conversation } from "../conversation/Conversation";
import { <PERSON><PERSON>r<PERSON>and<PERSON> } from "../utils/ErrorHandler";
import { PerformanceMonitor } from "../utils/PerformanceMonitor";
import { ChatPersistenceManager } from "./ChatPersistenceManager";

export class ChatModel {
  //对话列表
  //TODO: 当前使用的是数组和 indexOf 方法，
  // 时间复杂度为 O(n)，使用 Map 或对象来存储对话，以 O(1) 时间复杂度查找。
  // 涉及调整范围：当前对话模型类实现，ChatPanel.ts和ChatController.ts, extension.ts
  conversations: Array<Conversation> = [];
  //当前选中的对话ID
  selectedConversationId: string | undefined;
  //持久化管理器
  private persistenceManager?: ChatPersistenceManager;

  setPersistenceManager(persistenceManager: ChatPersistenceManager) {
    this.persistenceManager = persistenceManager;
  }

  addAndSelectConversation(conversation: Conversation) {
    this.conversations.push(conversation);
    this.selectedConversationId = conversation.id;
    this.saveState();
  }

  getConversationById(id: string): Conversation | undefined {
    return this.conversations.find((conversation) => conversation.id === id);
  }

  /**
   * 删除指定的对话。
   * 如果删除的对话是当前选中的对话，则同时清除选中的对话ID。
   * 删除后会尝试保存状态，若保存失败会记录错误日志。
   *
   * @param id - 要删除的对话ID。
   */
  deleteConversation(id: string) {
    try {
      // 删除指定的对话
      const index = this.conversations.findIndex((conversation) => conversation.id === id);
      if (index !== -1) {
        this.conversations.splice(index, 1);
      }

      // 如果删除的对话是当前选中的对话,则清除当前选中的对话ID。
      if (this.selectedConversationId === id) {
        this.selectedConversationId = undefined;
      }

      // 更新对话列表并持久化数据
      this.saveState();
    } catch (error) {
      ErrorHandler.handleError(
        error,
        'ChatModel.deleteConversation',
        {
          showToUser: false,
          logLevel: 'error',
          userMessage: 'Failed to delete conversation'
        }
      );
    }
  }

  /**
   * 设置选中的对话。
   * 如果对话存在，则更新选中的对话ID并保存状态；否则，记录错误信息。
   *
   * @param id - 对话的唯一标识符。
   * @throws 如果尝试选中不存在的对话，会触发错误处理。
   */
  setSelectedConversation(id: string) {
    const conversation = this.getConversationById(id);
    if (conversation) {//验证对话是否存在
      this.selectedConversationId = id;
      this.saveState();
    } else {
      ErrorHandler.handleError(
        new Error(`Attempted to select non-existent conversation: ${id}`),
        'ChatModel.setSelectedConversation',
        {
          showToUser: false,
          logLevel: 'warn',
          userMessage: 'Failed to select non-existent conversation'
        }
      );
    }
  }

  /**
   * 持久化当前对话状态对象
   * 
   * @private
   * @returns Promise<void>
   * @description 内部方法，使用persistenceManager保存当前对话列表和选中的对话ID
   * 如果保存过程中发生错误，会通过ErrorHandler处理错误，并通过"输出"面板显示，但不中断程序执行
   */
  private async saveState() {
    if (!this.persistenceManager) return;

    try {
      await PerformanceMonitor.timeAsync(
        'chat-model-save-state',
        () => this.persistenceManager?.saveConversations(this.conversations, this.selectedConversationId) ?? Promise.resolve()
      );
    } catch (error) {
      await ErrorHandler.handleError(
        error,
        'ChatModel.saveState',
        {
          showToUser: false,
          logLevel: 'warn',
          userMessage: 'Failed to save conversation state'
        }
      );
    }
  }

  /**
   * 异步加载对话状态对象
   * 
   * 从持久化存储中加载对话列表和当前选中的对话ID，并进行有效性校验。
   * 如果加载失败会通过错误处理器记录日志，并通过"输出"面板显示，但不中断程序执行。
   * 
   * @returns Promise<void> 异步操作，无返回值
   */
  async loadState(): Promise<void> {
    if (!this.persistenceManager) return;

    try {
      const result = await PerformanceMonitor.timeAsync(
        'chat-model-load-state',
        () => this.persistenceManager?.loadConversations() ?? Promise.resolve(null)
      );

      if (result) {
        this.conversations = result.conversations;
        this.selectedConversationId = result.selectedConversationId;

        // Validate selectedConversationId after loading
        if (this.selectedConversationId && !this.getConversationById(this.selectedConversationId)) {
          console.warn(`Selected conversation ${this.selectedConversationId} not found in loaded conversations, clearing selection`);
          this.selectedConversationId = undefined;
        }
      }
    } catch (error) {
      await ErrorHandler.handleError(
        error,
        'ChatModel.loadState',
        {
          showToUser: false,
          logLevel: 'warn',
          userMessage: 'Failed to load conversation history'
        }
      );
    }
  }
}
