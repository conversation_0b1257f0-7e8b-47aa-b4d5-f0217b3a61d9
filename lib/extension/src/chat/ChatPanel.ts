import { webviewApi } from "@codebeat/common";
import * as vscode from "vscode";
import { APIKeyManager } from "../ai/ApiKeyManager";
import { PerformanceMonitor } from "../utils/PerformanceMonitor";
import { WebviewContainer } from "../webview/WebviewContainer";
import { ChatModel } from "./ChatModel";

function getConfigSurfacePromptForOpenAIPlus(): boolean {
  return vscode.workspace
    .getConfiguration("codebeat.openAI")
    .get<boolean>("surfacePromptForPlus", false);
}

export class ChatPanel implements vscode.WebviewViewProvider {
  public static readonly id = "codebeat.chat";

  private readonly disposables: vscode.Disposable[] = [];

  private messageEmitter = new vscode.EventEmitter<unknown>();

  readonly onDidReceiveMessage = this.messageEmitter.event;

  private readonly extensionUri: vscode.Uri;

  private webviewPanel: WebviewContainer | undefined;
  private apiKeyManager: APIKeyManager;

  private state: webviewApi.PanelState;

  constructor({
    extensionUri,
    apiKeyManager,
    hasOpenAIApiKey,
  }: {
    readonly extensionUri: vscode.Uri;
    apiKeyManager: APIKeyManager;
    /** Needed since retrieving it is an async operation */
    hasOpenAIApiKey: boolean;
  }) {
    this.extensionUri = extensionUri;
    this.apiKeyManager = apiKeyManager;

    const surfacePromptForOpenAIPlus = getConfigSurfacePromptForOpenAIPlus();
    this.state = {
      type: "chat",
      selectedConversationId: undefined,
      conversations: [],
      hasOpenAIApiKey,
      surfacePromptForOpenAIPlus,
    };

    this.apiKeyManager.onUpdate(async () => {
      if (this.state?.type !== "chat") {
        return;
      }

      const hasOpenAIApiKey = await this.apiKeyManager.hasOpenAIApiKey();
      if (this.state.hasOpenAIApiKey === hasOpenAIApiKey) {
        return;
      }

      this.state.hasOpenAIApiKey = hasOpenAIApiKey;
      this.renderPanel();
    });
  }

  private async renderPanel() {
    try {
      console.log("CodeBeat ChatPanel: Rendering panel", {
        hasWebviewPanel: !!this.webviewPanel,
        state: this.state ? {
          type: this.state.type,
          conversationCount: this.state.type === "chat" ? this.state.conversations?.length : 0
        } : null
      });

      const result = await this.webviewPanel?.updateState(this.state);
      console.log("CodeBeat ChatPanel: Panel rendered successfully", { result });
      return result;
    } catch (error) {
      console.error("CodeBeat ChatPanel: Failed to render panel", error);
      throw error;
    }
  }

  async resolveWebviewView(webviewView: vscode.WebviewView) {
    this.webviewPanel = new WebviewContainer({
      panelId: "chat",
      isStateReloadingEnabled: false,
      webview: webviewView.webview,
      extensionUri: this.extensionUri,
    });

    const receiveMessageDisposable = this.webviewPanel.onDidReceiveMessage(
      (message: unknown) => {
        this.messageEmitter.fire(message);
      }
    );

    this.disposables.push(
      webviewView.onDidDispose(() => {
        receiveMessageDisposable.dispose();
        this.webviewPanel = undefined;
      })
    );

    this.disposables.push(
      webviewView.onDidChangeVisibility(async () => {
        if (webviewView.visible) {
          return this.renderPanel();
        }
      })
    );

    // not using await here, to avoid having an infinite load-in-progress indicator
    this.renderPanel();
  }

  /**
   * 更新对话面板的状态并渲染面板。
   * 该方法会异步执行以下操作：
   * 1. 将当前对话模型中的对话转换为适用于Webview的格式。
   * 2. 检查是否配置了OpenAI API密钥。
   * 3. 更新面板的状态，包括选中的对话ID、对话列表、API密钥状态等。
   * 4. 调用渲染方法重新渲染面板。
   *
   * @param model 当前的对话模型，包含对话列表和选中的对话ID。
   * @returns 返回渲染后的面板内容。
   */
  async update(model: ChatModel) {
    return await PerformanceMonitor.timeAsync(
      'chat-panel-update',
      async () => {
        const conversations: Array<webviewApi.Conversation> = [];
        for (const conversation of model.conversations) {
          conversations.push(await conversation.toWebviewConversation());
        }

        const surfacePromptForOpenAIPlus = getConfigSurfacePromptForOpenAIPlus();
        const hasOpenAIApiKey = await this.apiKeyManager.hasOpenAIApiKey();
        this.state = {
          type: "chat",
          selectedConversationId: model.selectedConversationId,
          conversations,
          hasOpenAIApiKey,
          surfacePromptForOpenAIPlus,
        };
        return this.renderPanel();
      },
      { conversationCount: model.conversations.length }
    );
  }

  dispose() {
    this.disposables.forEach((disposable) => disposable.dispose());
  }
}
