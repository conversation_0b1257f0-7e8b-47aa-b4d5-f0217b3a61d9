import { util, webviewApi } from "@codebeat/common";
import * as vscode from "vscode";
import { AIClient } from "../ai/AIClient";
import { Conversation } from "../conversation/Conversation";
import { ConversationType } from "../conversation/ConversationType";
import { resolveVariables } from "../conversation/input/resolveVariables";
import { DiffEditorManager } from "../diff/DiffEditorManager";
import { ChatModel } from "./ChatModel";
import { ChatPanel } from "./ChatPanel";

export class ChatController {
  private readonly chatPanel: ChatPanel;
  private readonly chatModel: ChatModel;
  private readonly ai: AIClient;
  private readonly getConversationType: (
    id: string
  ) => ConversationType | undefined;
  private readonly diffEditorManager: DiffEditorManager;
  private readonly basicChatTemplateId: string;
  private readonly generateConversationId: () => string;
  private readonly creationInProgress = new Set<string>();

  constructor({
    chatPanel,
    chatModel,
    ai,
    getConversationType,
    diffEditorManager,
    basicChatTemplateId,
  }: {
    chatPanel: ChatPanel;
    chatModel: ChatModel;
    ai: AIClient;
    getConversationType: (id: string) => ConversationType | undefined;
    diffEditorManager: DiffEditorManager;
    basicChatTemplateId: string;
  }) {
    this.chatPanel = chatPanel;
    this.chatModel = chatModel;
    this.ai = ai;
    this.getConversationType = getConversationType;
    this.diffEditorManager = diffEditorManager;
    this.basicChatTemplateId = basicChatTemplateId;

    this.generateConversationId = util.createNextId({
      prefix: "conversation-",
    });
  }

  async updateChatPanel() {
    await this.chatPanel.update(this.chatModel);
  }
  /**
   * 添加并显示对话。
   * 
   * @param conversation 要添加和显示的对话对象。
   * @returns 返回添加后的对话对象。
   * @private 这是一个私有方法，仅在当前类内部使用。
   */
  private async addAndShowConversation<T extends Conversation>(
    conversation: T
  ): Promise<T> {
    //将对话添加到对话模型，并标记为选中状态（即：当前对话）。
    this.chatModel.addAndSelectConversation(conversation);
    await this.showChatPanel();// 展开对话面板（ChatPanel）
    await this.updateChatPanel(); // 更新对话面板的展示。

    return conversation;
  }

  async showChatPanel() {
    await vscode.commands.executeCommand("codebeat.chat.focus");
  }

  /**
   * 处理ChatPanel接收到的消息，并根据消息类型执行相应的操作。
   *
   * @param rawMessage - 原始消息数据，类型为未知。
   * @throws {Error} 如果消息类型不支持，抛出错误。
   *
   * 支持的消息类型及操作：
   * - "enterOpenAIApiKey": 执行命令以输入OpenAI API密钥。
   * - "clickCollapsedConversation": 设置选中的对话并更新聊天面板。
   * - "sendMessage": 发送消息到指定对话。
   * - "startChat": 创建一个新的对话。
   * - "deleteConversation": 删除指定对话并更新聊天面板。
   * - "exportConversation": 将指定对话导出为Markdown格式。
   * - "retry": 重试指定对话的操作。
   * - "dismissError": 清除指定对话的错误状态。
   * - "insertPromptIntoEditor": 将提示插入到编辑器中。
   * - "applyDiff" 和 "reportError": 由Conversation内部处理（架构规划缺陷）。
   */
  async receivePanelMessage(rawMessage: unknown) {
    const message = webviewApi.outgoingMessageSchema.parse(rawMessage);
    const type = message.type;

    switch (type) {
      case "enterOpenAIApiKey": {
        await vscode.commands.executeCommand("codebeat.enterOpenAIApiKey");
        break;
      }
      case "clickCollapsedConversation": {
        this.chatModel.setSelectedConversation(message.data.id);
        await this.updateChatPanel();
        break;
      }
      case "sendMessage": {
        await this.chatModel
          .getConversationById(message.data.id)
          ?.answer(message.data.message);
        break;
      }
      case "startChat": {
        await this.createConversationSafe(this.basicChatTemplateId);
        break;
      }
      case "deleteConversation": {
        this.chatModel.deleteConversation(message.data.id);
        await this.updateChatPanel();
        break;
      }
      case "exportConversation": {
        await this.chatModel
          .getConversationById(message.data.id)
          ?.exportMarkdown();
        break;
      }
      case "retry": {
        await this.chatModel.getConversationById(message.data.id)?.retry();
        break;
      }
      case "dismissError":
        await this.chatModel
          .getConversationById(message.data.id)
          ?.dismissError();
        break;
      case "insertPromptIntoEditor":
        await this.chatModel
          .getConversationById(message.data.id)
          ?.insertPromptIntoEditor();
        break;
      case "applyDiff":
      case "reportError": {
        // Architecture debt: there are 2 views, but 1 outgoing message type
        // These are handled in the Conversation
        //TODO 架构规划缺陷-债务：有2个视图，但有1个传出消息类型
        //                     这些暂时已经在Conversation中处理
        break;
      }
      default: {
        const exhaustiveCheck: never = type;
        throw new Error(`unsupported type: ${exhaustiveCheck}`);
      }
    }
  }

  /**
   * 安全地创建对话，避免重复创建。
   * 如果对话类型ID的创建已经在进行中，则直接返回。
   *
   * @param conversationTypeId - 对话类型ID，用于标识要创建的对话类型。
   * @returns 无返回值。
   */
  async createConversationSafe(conversationTypeId: string) {
    // Prevent duplicate creation
    if (this.creationInProgress.has(conversationTypeId)) {
      console.log(`Conversation creation already in progress for: ${conversationTypeId}`);
      return;
    }

    this.creationInProgress.add(conversationTypeId);

    try {
      await this.createConversation(conversationTypeId);
    } finally {
      this.creationInProgress.delete(conversationTypeId);
    }
  }

  /**
   * 创建一个新的对话。
   * 
   * @param conversationTypeId - 对话类型的唯一标识符。
   * @throws 如果找不到对应的对话类型，抛出错误。
   * @throws 如果创建对话过程中发生错误，抛出错误并显示错误信息。
   * 
   * 功能说明：
   * 1. 检测现有对话ID以避免冲突。
   * 2. 根据提供的对话类型ID获取对话类型配置。
   * 3. 解析对话类型中的变量。
   * 4. 调用对话类型的创建方法生成新对话。
   * 5. 处理创建结果：
   *    - 如果结果为“unavailable”，显示提示或错误信息。
   *    - 如果创建成功，将对话添加到界面并显示。
   *    - 如果配置为立即回答，触发对话的自动回答功能。
   * 6. 捕获并处理创建过程中的异常。
   */
  async createConversation(conversationTypeId: string) {
    try {
      // 获取现有对话ID以防止冲突
      const existingIds = new Set(this.chatModel.conversations.map(c => c.id));
      // 根据提供的ID获取ConversationType
      const conversationType = this.getConversationType(conversationTypeId);

      if (conversationType == undefined) {
        throw new Error(`No conversation type found for ${conversationTypeId}`);
      }

      // 解析对话类型中的变量，即：对话类型模板中约定的变量，如：选中文本(selectedText)、文件(file)
      const variableValues = await resolveVariables(
        conversationType.variables,
        {
          time: "conversation-start",
        }
      );
      //创建新对话
      const result = await conversationType.createConversation({
        conversationId: util.createUniqueConversationId(existingIds),
        ai: this.ai,
        updateChatPanel: this.updateChatPanel.bind(this),//绑定当前新对话的对话面板(Chat panel)的消息处理器。
        diffEditorManager: this.diffEditorManager,
        initVariables: variableValues,
      });

      // TODO 处理不可用的情况
      if (result.type === "unavailable") {
        if (result.display === "info") {
          await vscode.window.showInformationMessage(result.message);
        } else if (result.display === "error") {
          await vscode.window.showErrorMessage(result.message);
        } else {
          await vscode.window.showErrorMessage("Required input unavailable");
        }
        return;
      }
      // 将新建对话添加到对话界面(ChatPanel)并显示
      await this.addAndShowConversation(result.conversation);

      if (result.shouldImmediatelyAnswer) {
        await result.conversation.answer();
      }
    } catch (error: unknown) {
      console.error("Failed to create conversation:", error);
      const errorMessage = error instanceof Error ? error.message : String(error);
      await vscode.window.showErrorMessage(`Failed to create conversation: ${errorMessage}`);
    }
  }
}
