import * as vscode from "vscode";
import { z } from "zod";
import { AIClient } from "../ai/AIClient";
import { Conversation } from "../conversation/Conversation";
import { ConversationType } from "../conversation/ConversationType";
import { DiffEditorManager } from "../diff/DiffEditorManager";
import { ErrorHandler } from "../utils/ErrorHandler";

// Schema for persisted conversation data
const persistedConversationSchema = z.object({
  id: z.string(),
  templateId: z.string(),
  messages: z.array(z.object({
    author: z.enum(["user", "bot"]),
    content: z.string(),
  })),
  state: z.object({
    type: z.string(),
  }).passthrough(),
  error: z.object({
    level: z.enum(["error", "warning"]).optional(),
    disableRetry: z.boolean().optional(),
    disableDismiss: z.boolean().optional(),
    message: z.string(),
    title: z.string(),
  }).optional(),
  initVariables: z.record(z.string(), z.unknown()),
  createdAt: z.number(),
  updatedAt: z.number(),
});

const persistedChatStateSchema = z.object({
  conversations: z.array(persistedConversationSchema),
  selectedConversationId: z.string().optional(),
  version: z.number(),
});

type PersistedConversation = z.infer<typeof persistedConversationSchema>;
type PersistedChatState = z.infer<typeof persistedChatStateSchema>;

export interface ChatPersistenceManager {
  saveConversations(conversations: Conversation[], selectedId?: string): Promise<void>;
  loadConversations(): Promise<{ conversations: Conversation[], selectedConversationId?: string } | null>;
  clearConversations(): Promise<void>;
}

export class WorkspaceChatPersistenceManager implements ChatPersistenceManager {
  private static readonly STORAGE_KEY = 'codebeat.chat.conversations';
  private static readonly CURRENT_VERSION = 1;
  private static readonly MAX_CONVERSATIONS = 50; // Limit to prevent storage bloat
  private static readonly MAX_AGE_DAYS = 30; // Auto-cleanup old conversations
  private saveInProgress = false; // Prevent concurrent saves

  constructor(
    private readonly context: vscode.ExtensionContext,
    private readonly getConversationType: (id: string) => ConversationType | undefined,
    private readonly ai: AIClient,
    private readonly diffEditorManager: DiffEditorManager,
    private readonly updateChatPanel: () => Promise<void>
  ) { }

  /**
   * 持久化对话列表
   * 
   * @param conversations - 要保存的对话列表
   * @param selectedId - 当前选中的对话ID（可选）
   * 
   * @remarks
   * 该方法会：
   * 1. 防止并发保存以避免数据损坏
   * 2. 将对话转换为可持久化的格式
   * 3. 只保留最近的MAX_CONVERSATIONS条对话
   * 4. 处理序列化错误并跳过有问题的对话
   * 5. 更新工作区状态存储
   * 
   * 注意：该方法会捕获所有错误并通过ErrorHandler处理
   */
  async saveConversations(conversations: Conversation[], selectedId?: string): Promise<void> {
    // Prevent concurrent saves to avoid data corruption
    if (this.saveInProgress) {
      console.warn('Save already in progress, skipping concurrent save request');
      return;
    }

    try {
      this.saveInProgress = true;

      const now = Date.now();
      const persistedConversations: PersistedConversation[] = [];

      // 仅取最近的MAX_CONVERSATIONS条对话
      for (const conversation of conversations.slice(-WorkspaceChatPersistenceManager.MAX_CONVERSATIONS)) {
        try {
          // 将对话转换为可持久化的格式(webview conversation)
          const webviewConversation = await conversation.toWebviewConversation();

          // 提取需要持久化的数据
          const error = webviewConversation.content.error;
          const persistedConversation: PersistedConversation = {
            id: conversation.id,
            templateId: (conversation as any).template?.id || 'chat-en', // Fallback to default
            messages: webviewConversation.content.type === 'messageExchange'
              ? webviewConversation.content.messages
              : [],
            state: webviewConversation.content.state, //
            error: typeof error === 'string' ? undefined : error, // Only persist object errors
            initVariables: (conversation as any).initVariables || {},
            createdAt: (conversation as any).createdAt || now,
            updatedAt: now,
          };

          persistedConversations.push(persistedConversation);
        } catch (error) {
          // Skip conversations that can't be serialized
          await ErrorHandler.handleError(
            error,
            'ChatPersistenceManager.saveConversations.serialize',
            { showToUser: false, logLevel: 'warn' }
          );
        }
      }

      const persistedState: PersistedChatState = {
        conversations: persistedConversations,
        selectedConversationId: selectedId,
        version: WorkspaceChatPersistenceManager.CURRENT_VERSION,
      };

      await this.context.workspaceState.update(
        WorkspaceChatPersistenceManager.STORAGE_KEY,
        persistedState
      );

    } catch (error) {
      await ErrorHandler.handleError(
        error,
        'ChatPersistenceManager.saveConversations',
        { showToUser: false, logLevel: 'error' }
      );
    } finally {
      this.saveInProgress = false;
    }
  }

  /**
   * 从工作区存储中加载并恢复对话数据
   * 
   * 该方法会：
   * 1. 检查存储数据的版本兼容性
   * 2. 过滤掉过期的对话（超过MAX_AGE_DAYS）
   * 3. 尝试恢复每个对话的状态
   * 4. 处理恢复失败的情况（创建基础对话或跳过）
   * 
   * @returns 返回包含对话列表和当前选中对话ID的对象，若加载失败则返回null
   * @throws 当遇到严重错误时会抛出异常（通过ErrorHandler处理）
   */
  async loadConversations(): Promise<{ conversations: Conversation[], selectedConversationId?: string } | null> {
    try {
      // 从工作区存储中加载对话数据
      const stored = this.context.workspaceState.get<unknown>(
        WorkspaceChatPersistenceManager.STORAGE_KEY
      );

      if (!stored) return null;
      // 利用Zod对对话数据进行类型检查,数据验证和转换
      const persistedState = persistedChatStateSchema.parse(stored);

      // 检查对话数据版本兼容性
      if (persistedState.version !== WorkspaceChatPersistenceManager.CURRENT_VERSION) {
        // TODO : Add migration logic here, 目前仅清除旧数据
        await this.clearConversations();
        return null;
      }

      const now = Date.now();
      const maxAge = WorkspaceChatPersistenceManager.MAX_AGE_DAYS * 24 * 60 * 60 * 1000;
      const conversations: Conversation[] = [];

      // 恢复对话
      for (const persistedConv of persistedState.conversations) {
        try {
          // 跳过过期的对话
          if (now - persistedConv.updatedAt > maxAge) {
            continue;
          }

          const conversationType = this.getConversationType(persistedConv.templateId);
          if (!conversationType) {
            // 对话模板不再存在，跳过此对话
            continue;
          }

          let conversation;
          try {
            // 创建对比对话实例。
            conversation = await conversationType.createConversation({
              conversationId: persistedConv.id,
              ai: this.ai,
              updateChatPanel: this.updateChatPanel,
              diffEditorManager: this.diffEditorManager,// 传入当前diffEditorManager
              initVariables: persistedConv.initVariables,
            });
          } catch {
            // 如果对话创建失败（例如，没有活动编辑器的diff对话），创建一个基本的对话，不带diff数据
            try {
              const { Conversation } = await import('../conversation/Conversation');

              // Safely access template property
              const template = 'getTemplate' in conversationType ? conversationType.getTemplate() : undefined;
              if (!template) {
                console.warn(`No template found for conversation type: ${persistedConv.templateId}`);
                continue; // Skip this conversation
              }

              conversation = {
                type: 'success' as const,
                conversation: new Conversation({
                  id: persistedConv.id,
                  initVariables: persistedConv.initVariables,
                  ai: this.ai,
                  updateChatPanel: this.updateChatPanel,
                  template: template,
                  diffEditorManager: this.diffEditorManager,
                  diffData: undefined,
                }),
                shouldImmediatelyAnswer: false,
              };
            } catch (fallbackError) {
              // If even fallback creation fails, skip this conversation
              console.warn(`Failed to create fallback conversation for ${persistedConv.id}:`, fallbackError);
              continue;
            }
          }

          // Restore the conversation state
          if (conversation.type === 'success') {
            const conv = conversation.conversation;

            try {
              // Use the dedicated restoration method
              if ('restoreFromPersisted' in conv && typeof conv.restoreFromPersisted === 'function') {
                conv.restoreFromPersisted({
                  messages: persistedConv.messages,
                  state: persistedConv.state,
                  error: persistedConv.error,
                  createdAt: persistedConv.createdAt,
                });
              } else {
                console.warn(`Conversation ${persistedConv.id} does not support restoration`);
              }

              conversations.push(conv);
            } catch (restoreError) {
              console.warn(`Failed to restore conversation state for ${persistedConv.id}:`, restoreError);
              // Still add the conversation but with default state
              conversations.push(conv);
            }
          }
        } catch (error) {
          // Skip conversations that can't be restored
          await ErrorHandler.handleError(
            error,
            'ChatPersistenceManager.loadConversations.restore',
            { showToUser: false, logLevel: 'warn' }
          );
        }
      }

      return {
        conversations,
        selectedConversationId: persistedState.selectedConversationId,
      };

    } catch (error) {
      await ErrorHandler.handleError(
        error,
        'ChatPersistenceManager.loadConversations',
        { showToUser: false, logLevel: 'error' }
      );
      return null;
    }
  }

  async clearConversations(): Promise<void> {
    try {
      await this.context.workspaceState.update(
        WorkspaceChatPersistenceManager.STORAGE_KEY,
        undefined
      );
    } catch (error) {
      await ErrorHandler.handleError(
        error,
        'ChatPersistenceManager.clearConversations',
        { showToUser: false, logLevel: 'error' }
      );
    }
  }
}
