import * as vscode from "vscode";
import { z } from "zod";
import { Logger } from "../logger";
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from "../utils/ErrorHandler";

// Configuration schema for type safety and validation
const configurationSchema = z.object({
  // AI Configuration
  model: z.string().default("deepseek-r1:1.5b"),
  openaiBaseUrl: z.string().url().default("https://api.openai.com/v1"),
  ollamaBaseUrl: z.string().url().default("http://localhost:11434"),

  // Performance Configuration
  maxTokens: z.number().min(1).max(100000).default(4000),
  temperature: z.number().min(0).max(2).default(0.7),
  timeout: z.number().min(1000).max(300000).default(30000), // 30 seconds

  // Cache Configuration
  cacheEnabled: z.boolean().default(true),
  cacheTtl: z.number().min(1000).max(300000).default(5000), // 5 seconds
  maxCacheSize: z.number().min(10).max(10000).default(1000),

  // Logging Configuration
  logLevel: z.enum(["debug", "info", "warning", "error"]).default("info"),
  enablePerformanceMonitoring: z.boolean().default(true),
  enableErrorReporting: z.boolean().default(true),

  // UI Configuration
  enableAutoSave: z.boolean().default(true),
  enableNotifications: z.boolean().default(true),
  maxConversationHistory: z.number().min(1).max(1000).default(100),
});

export type Configuration = z.infer<typeof configurationSchema>;

interface ConfigurationCache {
  config: Configuration;
  lastUpdated: number;
  ttl: number;
}

/**
 * Centralized configuration manager for the CodeBeat extension
 * 
 * Features:
 * - Type-safe configuration with Zod validation
 * - Intelligent caching with configurable TTL
 * - Configuration change monitoring
 * - Fallback and default value handling
 * - Hot reload support
 */
export class ConfigurationManager {
  private static instance: ConfigurationManager;
  private cache: ConfigurationCache | null = null;
  private readonly logger: Logger;
  private readonly disposables: vscode.Disposable[] = [];
  private readonly changeListeners: Array<(config: Configuration) => void> = [];

  private constructor(logger: Logger) {
    this.logger = logger;
    this.setupConfigurationWatcher();

    this.logger.log("✅ Configuration manager initialized");
  }

  /**
   * Get singleton instance
   */
  static getInstance(logger?: Logger): ConfigurationManager {
    if (!ConfigurationManager.instance) {
      if (!logger) {
        throw new Error("Logger is required for first initialization");
      }
      ConfigurationManager.instance = new ConfigurationManager(logger);
    }
    return ConfigurationManager.instance;
  }

  /**
   * Get current configuration with caching
   */
  getConfiguration(): Configuration {
    const now = Date.now();

    // Return cached config if still valid
    if (this.cache && (now - this.cache.lastUpdated) < this.cache.ttl) {
      return this.cache.config;
    }

    // Load fresh configuration
    const config = this.loadConfiguration();

    // Update cache
    this.cache = {
      config,
      lastUpdated: now,
      ttl: config.cacheTtl,
    };

    return config;
  }

  /**
   * Get specific configuration value
   */
  get<K extends keyof Configuration>(key: K): Configuration[K] {
    return this.getConfiguration()[key];
  }

  /**
   * Update configuration value
   */
  async set<K extends keyof Configuration>(
    key: K,
    value: Configuration[K]
  ): Promise<void> {
    try {
      const config = vscode.workspace.getConfiguration("codebeat");
      await config.update(key, value, vscode.ConfigurationTarget.Global);

      // 更新配置前，先清理缓存
      this.clearCache();

      this.logger.log(`Configuration updated: ${key} = ${JSON.stringify(value)}`);
    } catch (error) {
      await ErrorHandler.handleError(
        error,
        "ConfigurationManager.set",
        { userMessage: `Failed to update configuration: ${key}` }
      );
    }
  }

  /**
   * Register configuration change listener
   * TODO 配置变更联动. 仅对AIClient默认实例生效? 覆盖模板中的设定?
   */
  onConfigurationChange(listener: (config: Configuration) => void): vscode.Disposable {
    this.changeListeners.push(listener);

    return new vscode.Disposable(() => {
      const index = this.changeListeners.indexOf(listener);
      if (index >= 0) {
        this.changeListeners.splice(index, 1);
      }
    });
  }

  /**
   * Validate configuration
   */
  validateConfiguration(config: unknown): Configuration {
    try {
      return configurationSchema.parse(config);
    } catch (error) {
      this.logger.warn([
        "Invalid configuration detected, using defaults",
        `Validation error: ${error instanceof Error ? error.message : String(error)}`
      ]);

      // Return default configuration
      return configurationSchema.parse({});
    }
  }

  /**
   * Get configuration diagnostics
   */
  getDiagnostics(): {
    cacheStatus: "hit" | "miss" | "disabled";
    cacheAge: number;
    configurationSource: "cache" | "vscode";
    validationStatus: "valid" | "invalid";
    lastUpdated: string;
  } {
    const now = Date.now();
    const cacheValid = this.cache && (now - this.cache.lastUpdated) < this.cache.ttl;

    return {
      cacheStatus: this.cache ? (cacheValid ? "hit" : "miss") : "disabled",
      cacheAge: this.cache ? now - this.cache.lastUpdated : 0,
      configurationSource: cacheValid ? "cache" : "vscode",
      validationStatus: "valid", // Always valid due to schema validation
      lastUpdated: this.cache ? new Date(this.cache.lastUpdated).toISOString() : "never",
    };
  }

  /**
   * 清理配置缓存
   */
  clearCache(): void {
    this.cache = null;
    this.logger.debug("Configuration cache invalidated and cleaned.");
  }

  /**
   * Dispose resources
   */
  dispose(): void {
    this.disposables.forEach(d => d.dispose());
    this.disposables.length = 0;
    this.changeListeners.length = 0;
    this.cache = null;
  }

  /**
   * Load configuration from VSCode settings
   */
  private loadConfiguration(): Configuration {
    try {
      // In test environment, return default configuration
      if (typeof vscode.workspace?.getConfiguration !== 'function') {
        return configurationSchema.parse({});
      }

      const config = vscode.workspace.getConfiguration("codebeat");

      const rawConfig = {
        model: config.get("model"),
        openaiBaseUrl: config.get("openaiBaseUrl"),
        ollamaBaseUrl: config.get("ollamaBaseUrl"),
        maxTokens: config.get("maxTokens"),
        temperature: config.get("temperature"),
        timeout: config.get("timeout"),
        cacheEnabled: config.get("cacheEnabled"),
        cacheTtl: config.get("cacheTtl"),
        maxCacheSize: config.get("maxCacheSize"),
        logLevel: config.get("logger.level"),
        enablePerformanceMonitoring: config.get("enablePerformanceMonitoring"),
        enableErrorReporting: config.get("enableErrorReporting"),
        enableAutoSave: config.get("enableAutoSave"),
        enableNotifications: config.get("enableNotifications"),
        maxConversationHistory: config.get("maxConversationHistory"),
      };

      return this.validateConfiguration(rawConfig);
    } catch (error) {
      this.logger.error([
        "Failed to load configuration from VSCode settings",
        `Error: ${error instanceof Error ? error.message : String(error)}`,
        "Using default configuration"
      ]);

      return configurationSchema.parse({});
    }
  }

  /**
   * Setup configuration change watcher
   */
  private setupConfigurationWatcher(): void {
    // Skip in test environment where vscode.workspace might not be available
    if (typeof vscode.workspace?.onDidChangeConfiguration !== 'function') {
      return;
    }

    const disposable = vscode.workspace.onDidChangeConfiguration((event) => {
      if (event.affectsConfiguration("codebeat")) {
        this.handleConfigurationChange();
      }
    });

    this.disposables.push(disposable);
  }

  /**
   * Handle configuration change
   */
  private handleConfigurationChange(): void {
    try {
      // Invalidate cache to force reload
      this.clearCache();

      // Get fresh configuration
      const newConfig = this.getConfiguration();

      // Notify listeners
      this.changeListeners.forEach(listener => {
        try {
          listener(newConfig);
        } catch (error) {
          this.logger.error([
            "Error in configuration change listener",
            `Error: ${error instanceof Error ? error.message : String(error)}`
          ]);
        }
      });

      this.logger.log("Configuration reloaded successfully");
    } catch (error) {
      ErrorHandler.handleError(
        error,
        "ConfigurationManager.handleConfigurationChange",
        { showToUser: false }
      );
    }
  }
}

/**
 * Configuration utility functions
 */
export const ConfigUtils = {
  /**
   * Check if a model is an Ollama model
   */
  isOllamaModel(model: string): boolean {
    const openaiModels = [
      "gpt-3.5-turbo", "gpt-4", "gpt-4-turbo", "gpt-4o",
      "gpt-4o-mini", "o1-preview", "o1-mini"
    ];
    return !openaiModels.includes(model);
  },

  /**
   * Get model display name
   */
  getModelDisplayName(model: string): string {
    const displayNames: Record<string, string> = {
      "deepseek-r1:1.5b": "DeepSeek R1 (1.5B)",
      "gpt-4o": "GPT-4o",
      "gpt-4-turbo": "GPT-4 Turbo",
      "gpt-3.5-turbo": "GPT-3.5 Turbo",
    };
    return displayNames[model] || model;
  },

  /**
   * Validate URL format
   */
  isValidUrl(url: string): boolean {
    try {
      new URL(url);
      return true;
    } catch {
      return false;
    }
  },
};
