import { Message } from "../Message";
import { Variable } from "../template/ConversationTemplate";
import { getFilename } from "./getFilename";
import { getGitDiff } from "./getGitDiff";
import { getLanguage } from "./getLanguage";
import { getOpenFiles } from "./getOpenFiles";
import { getSelectedLocationText } from "./getSelectedLocationText";
import { getSelectedText } from "./getSelectedText";
import { getSelectedTextWithDiagnostics } from "./getSelectionWithDiagnostics";
import { getWorkspaceFolder } from "./getWorkspaceFolder";

export async function resolveVariable(
  variable: Variable,
  { messages }: { messages?: Array<Message> } = {}
): Promise<unknown> {
  const variableType = variable.type;
  switch (variableType) {
    case "context":
      return getOpenFiles();
    case "constant":
      return variable.value;
    case "message":
      return messages?.at(variable.index)?.[variable.property];
    case "selected-text":
      return getSelectedText();
    case "selected-location-text":
      return getSelectedLocationText();
    case "filename":
      return getFilename();
    case "language":
      return getLanguage();
    case "selected-text-with-diagnostics":
      return getSelectedTextWithDiagnostics({
        diagnosticSeverities: variable.severities || ["error", "warning", "information", "hint"],
      });
    case "file-path":
      return getFilename(); // 使用相同的函数
    case "workspace-folder":
      return getWorkspaceFolder();
    case "git-diff":
      return getGitDiff();
    default: {
      const exhaustiveCheck: never = variableType;
      throw new Error(`unsupported type: ${exhaustiveCheck}`);
    }
  }
}
