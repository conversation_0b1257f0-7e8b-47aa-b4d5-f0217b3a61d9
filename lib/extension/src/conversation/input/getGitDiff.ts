import * as vscode from "vscode";
import { simpleGit } from "simple-git";

export async function getGitDiff(): Promise<string> {
  try {
    const workspaceFolder = vscode.workspace.workspaceFolders?.[0];
    if (!workspaceFolder) {
      return "No workspace folder found";
    }

    const git = simpleGit(workspaceFolder.uri.fsPath);
    const diff = await git.diff();
    
    return diff || "No git changes found";
  } catch (error) {
    return `Error getting git diff: ${error instanceof Error ? error.message : String(error)}`;
  }
}
