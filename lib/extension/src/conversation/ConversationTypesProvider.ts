import * as vscode from "vscode";
import { ErrorHandler } from "../utils/ErrorHandler";
import { ConversationType } from "./ConversationType";
import { loadConversationTplFromFile } from "./template/loadConversationTemplateFromFile";
import { loadConversationTplFromWorkspace } from "./template/loadConversationTemplatesFromWorkspace";
import { parseConversationTpl } from "./template/parseConversationTemplate";

export class ConversationTypesProvider {
  private readonly extensionUri: vscode.Uri;
  private readonly extensionTemplates: string[] = [];
  private readonly conversationTypes = new Map<string, ConversationType>();

  constructor({ extensionUri }: { extensionUri: vscode.Uri }) {
    this.extensionUri = extensionUri;
  }

  /**
   * 根据会话对话类型模板ID获取对应的会话类型
   * @param id 会话的唯一标识符
   * @returns 返回与ID关联的会话类型，如果不存在则返回undefined
   */
  getConversationType(id: string) {
    return this.conversationTypes.get(id);
  }

  /**
   * 获取所有对话类型模板（即：对话类型）
   * @returns 包含所有对话类型的数组副本
   */
  getConversationTypes() {
    return [...this.conversationTypes.values()];
  }

  /**
   * 注册来自VSCode扩展市场的对话类型模板
   * @param template - 要注册的模板字符串
   */
  registerExtensionTemplate({ template }: { template: string }) {
    this.extensionTemplates.push(template);
  }

  /**
   * 加载所有对话类型模板
   * 
   * 该方法会清空当前对话类型集合，并按顺序加载：
   * 1. 内置对话类型模板
   * 2. 来自VSCode扩展市场的对话类型模板  
   * 3. 工作区中自定义的对话类型模板
   */
  async loadConversationTypes() {
    this.conversationTypes.clear();

    await this.loadBuiltInTemplates();//内置
    await this.loadExtensionTemplates();//VSCode扩展市场
    await this.loadWorkspaceTemplates();//工作区
  }

  /**
   * 加载所有内置对话类型模板
   * 
   * 该方法异步加载预定义的内置对话类型模板文件，包括chat、func和task三种类型。
   * 加载完成后会将所有模板存储到 conversationTypes 映射中，使用模板ID作为键。
   * 
   */
  private async loadBuiltInTemplates() {
    const builtInConversationTypes = [
      await this.loadBuiltinTemplate("chat", "chat-en.cbt.md"),
      // 
      await this.loadBuiltinTemplate("fun", "class-hierarchy-diagram.cbt.md"),
      await this.loadBuiltinTemplate("fun", "function-call-graph.cbt.md"),
      //
      await this.loadBuiltinTemplate("task", "diagnose-errors.cbt.md"),
      await this.loadBuiltinTemplate("task", "document-code.cbt.md"),
      await this.loadBuiltinTemplate("task", "edit-code.cbt.md"),
      await this.loadBuiltinTemplate("task", "explain-code.cbt.md"),
      await this.loadBuiltinTemplate("task", "explain-code-w-context.cbt.md"),
      await this.loadBuiltinTemplate("task", "find-bugs.cbt.md"),
      await this.loadBuiltinTemplate("task", "generate-code.cbt.md"),
      await this.loadBuiltinTemplate("task", "generate-unit-test.cbt.md"),
      await this.loadBuiltinTemplate("task", "improve-readability.cbt.md"),
    ];

    for (const conversationType of builtInConversationTypes) {
      this.conversationTypes.set(conversationType.id, conversationType);
    }
  }

  /**
   * 加载单个内置对话类型模板
   * @param path 模板文件路径片段
   * @returns 返回解析后的ConversationType对象
   * @throws 当模板加载失败时抛出错误
   * @private 内部实现方法
   */
  private async loadBuiltinTemplate(...path: string[]) {
    const fileUri = vscode.Uri.joinPath(this.extensionUri, "template", ...path);
    const result = await loadConversationTplFromFile(fileUri);

    if (result.type === "error") {
      throw new Error(
        `Failed to load chat template '${fileUri.toString()}': ${result.error}`
      );
    }

    return new ConversationType({
      template: result.template,
      source: "built-in",
    });
  }

  /**
   * 加载已注册的扩展对话类型模板，并，解析为对话类型
   * @private 内部实现细节
   * @description 遍历所有扩展模板文本，尝试解析每个模板：
   * 1. 解析失败时记录错误并跳过
   * 2. 解析成功后将模板转换为ConversationType并存入conversationTypes映射表
   * 3. 捕获处理过程中所有未预期的错误
   */
  private async loadExtensionTemplates() {
    for (const templateText of this.extensionTemplates) {
      try {
        const result = parseConversationTpl(templateText);

        if (result.type === "error") {
          await ErrorHandler.handleError(
            result.error,
            'ConversationTypesProvider.loadExtensionTemplates',
            { userMessage: 'Could not load extension template' }
          );
          continue;
        }

        const template = result.template;
        this.conversationTypes.set(
          template.id,
          new ConversationType({
            template,
            source: "extension",
          })
        );
      } catch (error) {
        await ErrorHandler.handleError(
          error,
          'ConversationTypesProvider.loadExtensionTemplates.parse',
          { userMessage: 'Could not load extension template' }
        );
      }
    }
  }

  /**
   * 加载工作区中的对话类型模板
   * 
   * 该方法会从工作区下的".codebeat/templates"目录中加载所有对话类型模板文件，处理加载结果：
   * 1. 对于加载错误的模板会记录警告日志并跳过
   * 2. 对于禁用的模板会直接跳过
   * 3. 将有效的模板转换为ConversationType并存入conversationTypes映射中
   * 
   * @private 这是一个内部实现方法
   */
  private async loadWorkspaceTemplates() {
    const workspaceTemplateLoadingResults =
      await loadConversationTplFromWorkspace();
    for (const loadingResult of workspaceTemplateLoadingResults) {
      if (loadingResult.type === "error") {
        await ErrorHandler.handleError(
          loadingResult.error,
          'ConversationTypesProvider.loadWorkspaceTemplates',
          {
            userMessage: `Error loading conversation template from ${loadingResult.file.path}`,
            logLevel: 'warn'
          }
        );

        continue;
      }

      if (loadingResult.template.isEnabled === false) {
        continue;
      }

      const type = new ConversationType({
        template: loadingResult.template,
        source: "local-workspace",
      });
      this.conversationTypes.set(type.id, type);
    }
  }
}
