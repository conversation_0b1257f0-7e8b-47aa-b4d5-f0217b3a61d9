import * as vscode from "vscode";
import { AIClient } from "../ai/AIClient";
import { DiffEditorManager } from "../diff/DiffEditorManager";
import { Conversation } from "./Conversation";
import { DiffData } from "./DiffData";
import { ConversationTemplate } from "./template/ConversationTemplate";

export type CreateConversationResult =
  | {
    type: "success";
    conversation: Conversation;
    shouldImmediatelyAnswer: boolean;
  }
  | {
    type: "unavailable";
    display?: undefined;
  }
  | {
    type: "unavailable";
    display: "info" | "error";
    message: string;
  };

/**
 * 表示一个对话类型，包含模板信息和相关配置
 * 
 * @property {string} id - 对话类型的唯一标识符
 * @property {string} label - 显示名称
 * @property {string} description - 描述信息
 * @property {"built-in"|"local-workspace"|"extension"} source - 来源类型
 * @property {CodeBeatTemplate["variables"]} variables - 模板变量
 * 
 * @method getTemplate - 获取模板用于持久化存储（内部使用）
 * @method createConversation - 创建新的对话实例
 * @method hasDiffCompletionHandler - 检查是否包含差异补全处理器
 * @method getDiffData - 获取当前编辑器中的差异数据
 */
export class ConversationType {
  readonly id: string;
  readonly label: string;
  readonly description: string;
  readonly source: "built-in" | "local-workspace" | "extension";
  readonly variables: ConversationTemplate["variables"];

  private template: ConversationTemplate;

  constructor({
    template,
    source,
  }: {
    template: ConversationTemplate;
    source: ConversationType["source"];
  }) {
    this.template = template;

    this.id = template.id;
    this.label = template.label;
    this.description = template.description;
    this.source = source;
    this.variables = template.variables;
  }

  get tags(): ConversationTemplate["tags"] {
    return this.template.tags;
  }

  /**
   * Get the template for persistence purposes
   * @internal Used by ChatPersistenceManager
   */
  getTemplate(): ConversationTemplate {
    return this.template;
  }

  async createConversation({
    conversationId,
    ai,
    updateChatPanel,
    initVariables,
    diffEditorManager,
  }: {
    conversationId: string;
    ai: AIClient;
    updateChatPanel: () => Promise<void>;
    initVariables: Record<string, unknown>;
    diffEditorManager: DiffEditorManager;
  }): Promise<CreateConversationResult> {
    return {
      type: "success",
      conversation: new Conversation({
        id: conversationId,
        initVariables,
        ai: ai,
        updateChatPanel,
        template: this.template,
        diffEditorManager,
        diffData: await this.getDiffData(),
      }),
      shouldImmediatelyAnswer: this.template.initialMessage != null,
    };
  }

  hasDiffCompletionHandler(): boolean {
    const template = this.template;
    return (
      template.initialMessage?.completionHandler?.type ===
      "active-editor-diff" ||
      template.response.completionHandler?.type === "active-editor-diff"
    );
  }

  async getDiffData(): Promise<undefined | DiffData> {
    if (!this.hasDiffCompletionHandler()) {
      return undefined;
    }

    const activeEditor = vscode.window.activeTextEditor;

    if (activeEditor == null) {
      throw new Error("active editor required");
    }

    const document = activeEditor.document;
    const range = activeEditor.selection;
    const selectedText = document.getText(range);

    if (selectedText.trim().length === 0) {
      throw new Error("no selection");
    }

    const filename = document.fileName.split("/").pop();

    if (filename == undefined) {
      throw new Error("no filename");
    }

    return {
      filename,
      language: document.languageId,
      selectedText,
      range,
      editor: activeEditor,
    };
  }
}
