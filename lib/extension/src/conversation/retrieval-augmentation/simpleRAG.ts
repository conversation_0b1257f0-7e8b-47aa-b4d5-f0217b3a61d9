import Handlebars from "handlebars";
import secureJSON from "secure-json-parse";
import * as vscode from "vscode";
import { AIClient } from "../../ai/AIClient";
import { readFileContent } from "../../vscode/readFileContent";
import { RAGSetting } from "../template/ConversationTemplate";
import { cosineSimilarity } from "./cosineSimilarity";
import { embeddingFileSchema } from "./EmbeddingFile";

/**
 * 执行基于检索增强生成(RAG)的查询操作
 * 
 * 该函数通过以下步骤实现RAG:
 * 1. 加载指定的嵌入文件
 * 2. 使用Handlebars模板引擎渲染查询字符串
 * 3. 生成查询的嵌入向量
 * 4. 计算查询与文档块之间的余弦相似度
 * 5. 过滤并排序相似度结果
 * 6. 返回最相关的文档块
 *
 * @param ragSettings - RAG配置对象，包含文件路径、查询模板、阈值和最大结果数
 * @param initVariables - 初始上下文变量(如选中文本、文件信息等)，从上一轮回复中获取
 * @param variables - 附加运行时变量，由当前ChatPanel的对话框/编辑器等组件提供提供
 * @param ai - AIClient实例，用于生成嵌入向量
 * 
 * @returns 返回按相似度排序的文档块数组，每个块包含文件路径、位置和内容信息；如果发生错误则返回undefined
 */
export async function executeRAG({
  ragSettings: ragSettings,
  initVariables,
  variables,
  ai,
}: {
  ragSettings: RAGSetting;
  initVariables: Record<string, unknown>;
  variables: Record<string, unknown>;
  ai: AIClient;
}): Promise<
  | Array<{
    file: string;
    startPosition: number;
    endPosition: number;
    content: string;
  }>
  | undefined
> {
  // 加载嵌入文件
  const file = ragSettings.file;
  // 生成相对于当前工作区的文件URI
  const fileUri = vscode.Uri.joinPath(
    vscode.workspace.workspaceFolders?.[0]?.uri ?? vscode.Uri.file(""),
    ".codebeat/embedding",
    file
  );

  const fileContent = await readFileContent(fileUri);
  const parsedContent = secureJSON.parse(fileContent);
  const { chunks } = embeddingFileSchema.parse(parsedContent);

  // 应用变量模板渲染查询模板。
  // Note:
  // 展开运算符 (...)将自动合并initVariables和variables，以variables优先initVariables
  const query = Handlebars.compile(ragSettings.query, {
    noEscape: true,
  })({
    ...initVariables,
    ...variables,
  });

  const result = await ai.embedding({
    input: query,
  });

  if (result.type === "error") {
    console.log(result.errorMessage);
    return undefined;
  }

  const queryEmbedding = result.embedding!;

  const similarityChunks = chunks
    .map(({ start_position, end_position, content, file, embedding }) => ({
      file,
      startPosition: start_position,
      endPosition: end_position,
      content,
      similarity: cosineSimilarity(embedding, queryEmbedding),
    }))
    .filter(({ similarity }) => similarity >= ragSettings.threshold);

  similarityChunks.sort((a, b) => b.similarity - a.similarity);

  return similarityChunks
    .slice(0, ragSettings.maxResults)
    .map((chunk) => ({
      file: chunk.file,
      startPosition: chunk.startPosition,
      endPosition: chunk.endPosition,
      content: chunk.content,
    }));
}
