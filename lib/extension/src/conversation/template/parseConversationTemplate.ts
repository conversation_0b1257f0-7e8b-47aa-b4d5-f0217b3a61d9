import { marked } from "marked";
import secureJSON from "secure-json-parse";
import {
  ConversationPrompt,
  ConversationTemplate,
  conversationTemplateSchema,
} from "./ConversationTemplate";

export type ConversationTplParseResult =
  | {
    type: "success";
    template: ConversationTemplate;
  }
  | {
    type: "error";
    error: unknown;
  };

/**
 * 用于管理按Markdown语言标识分类(如:"json", "python")的代码片段集合
 * 
 * 内部使用 Map 存储不同语言对应的代码片段内容
 * 提供设置、获取代码片段的方法，以及解析 Handlebars 模板的功能
 * 
 * @property contentByLangInfo - 存储语言到代码片段的映射
 * @method set - 设置指定语言的代码片段
 * @method get - 获取指定语言的代码片段，不存在时抛出错误
 * @method resolveTemplate - 解析指定模板ID的 Handlebars 模板并设置到提示对象
 * @method getHandlebarsTemplate - 私有方法，获取并处理指定名称的 Handlebars 模板
 */
class NamedCodeSnippetMap {
  private readonly contentByLangInfo = new Map<string, string>();

  set(langInfo: string, content: string): void {
    this.contentByLangInfo.set(langInfo, content);
  }

  get(langInfo: string): string {
    const content = this.contentByLangInfo.get(langInfo);
    if (content == null) {
      throw new Error(`Code snippet for lang info '${langInfo}' not found.`);
    }
    return content;
  }

  /**
   * 解析并设置指定模板ID对应的Handlebars模板到prompt对象中
   * @param prompt - 需要设置模板的Prompt对象
   * @param templateId - 要解析的模板ID
   */
  resolvePromptTemplate(prompt: ConversationPrompt, templateId: string) {
    prompt.template = this.getHandlebarsTemplate(templateId);
  }
  /**
   * 从对话模板中获取并处理指定名称的Handlebars模板
   * 
   * @param templateName - 模板名称，将被添加"template-"前缀用于查找
   * @returns 返回处理后的模板字符串，替换转义的反引号
   * 
   * @private
   */
  private getHandlebarsTemplate(templateName: string): string {
    return this.get(`template-${templateName}`).replace(/\\`\\`\\`/g, "```");
  }
}

/**
 * 从Markdown内容中提取命名代码片段
 * 
 * @param content - 包含代码块的Markdown文本
 * @returns 返回一个NamedCodeSnippetMap对象，其中键为代码语言标识，值为对应代码内容
 * 
 * @remarks
 * 该函数使用marked.js解析Markdown，筛选出所有代码块，
 * 并将带有语言标识的代码块存入映射表中
 */
export const extractNamedCodeSnippets = (
  content: string
): NamedCodeSnippetMap => {
  const codeSnippets = new NamedCodeSnippetMap();

  marked
    .lexer(content)
    .filter((token) => token.type === "code")
    .forEach((token) => {
      const codeToken = token as marked.Tokens.Code;
      if (codeToken.lang != null) {
        codeSnippets.set(codeToken.lang, codeToken.text);
      }
    });

  return codeSnippets;
};

/**
 * 解析 CodeBeat 对话模板字符串并返回解析结果
 * 
 * @param templateAsCbtMarkdown - 包含模板的 CBT Markdown 格式字符串
 * @returns 解析结果对象，包含成功解析的模板或错误信息
 * 
 * 功能说明：
 * 1. 从 Markdown 中提取命名代码片段
 * 2. 解析 JSON 格式的对话模板
 * 3. 解析模板中的初始消息和响应内容
 * 4. 返回包含模板或错误信息的结构化结果
 */
export function parseConversationTpl(
  templateAsCbtMarkdown: string
): ConversationTplParseResult {
  try {
    const namedCodeSnippets = extractNamedCodeSnippets(templateAsCbtMarkdown);
    const templateText = namedCodeSnippets.get("json conversation-template");
    const template = conversationTemplateSchema.parse(
      secureJSON.parse(templateText)
    );

    //获取初始化对话的提示模板，并将其存储在提示对象中
    let initPrompt: ConversationPrompt | undefined;
    if (template.initialMessage != null) {
      initPrompt = template.initialMessage as ConversationPrompt;
      namedCodeSnippets.resolvePromptTemplate(
        initPrompt,
        "initial-message"
      );
    }
    //获取对话的提示模板，并将其存储在提示对象中
    let responsePrompt = template.response as ConversationPrompt | undefined;
    if (responsePrompt == null) {
      const err = new Error(
        "Error parsing conversation template: template-response not found."
      );
      return { type: "error", error: err, };
    }
    namedCodeSnippets.resolvePromptTemplate(
      responsePrompt,
      "response"
    );

    // 转换为正确的 ConversationTemplate 类型
    const conversationTpl: ConversationTemplate = {
      ...template,
      initialMessage: initPrompt,
      response: responsePrompt,
    };

    return { type: "success", template: conversationTpl, };
  } catch (err) {
    return { type: "error", error: err, };
  }
}
