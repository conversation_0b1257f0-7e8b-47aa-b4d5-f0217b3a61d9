import * as vscode from "vscode";
import { readFileContent } from "../../vscode/readFileContent";
import { ConversationTemplateLoadedResult } from "./ConversationTemplateLoadedResult";
import { parseConversationTpl } from "./parseConversationTemplate";

/**
 * 从文件加载对话类型模板
 * 
 * @param file - 要加载的文件URI
 * @returns 返回一个Promise，解析为模板加载结果。成功时包含模板内容，失败时包含错误信息
 * 
 * @remarks
 * 该函数会尝试读取并解析指定文件内容，返回成功或失败的结果对象。
 * 失败可能由文件读取错误或模板解析错误导致。
 */
export const loadConversationTplFromFile = async (
  file: vscode.Uri
): Promise<ConversationTemplateLoadedResult> => {
  try {
    const parseResult = parseConversationTpl(await readFileContent(file));

    if (parseResult.type === "error") {
      return {
        type: "error" as const,
        file,
        error: parseResult.error,
      };
    }

    return {
      type: "success" as const,
      file,
      template: parseResult.template,
    };
  } catch (error) {
    return {
      type: "error" as const,
      file,
      error,
    };
  }
};
