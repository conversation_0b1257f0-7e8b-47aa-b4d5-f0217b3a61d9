import * as vscode from "vscode";
import { ConversationTemplateLoadedResult } from "./ConversationTemplateLoadedResult";
import { loadConversationTplFromFile } from "./loadConversationTemplateFromFile";

const TEMPLATE_GLOB = ".codebeat/template/**/*.cbt.md";

/**
 * 从工作区加载所有对话类型模板文件
 * 
 * @returns 返回一个Promise，解析为包含所有模板加载结果的数组
 * 每个结果包含模板内容和加载状态信息
 */
export async function loadConversationTplFromWorkspace(): Promise<
  Array<ConversationTemplateLoadedResult>
> {
  const files = await vscode.workspace.findFiles(TEMPLATE_GLOB);
  return await Promise.all(files.map(loadConversationTplFromFile));
}
