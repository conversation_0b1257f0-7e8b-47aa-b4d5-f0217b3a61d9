import zod from "zod";

const completionHandlerSchema = zod.discriminatedUnion("type", [
  zod.object({
    type: zod.literal("message"),
  }),
  zod.object({
    type: zod.literal("update-temporary-editor"),
    botMessage: zod.string(),
    language: zod.string().optional(),
  }),
  zod.object({
    type: zod.literal("active-editor-diff"),
  }),
]);

/**
 * 定义RAG对象数据结构及校验约定
 * 
 * @property variableName - 对话模板中的变量名,即: {{name}}. 用于标识运行时寄存RAG检索结果,并应于模板中的变量对应。
 * @property type - 检索方法，目前仅支持"similarity-search"
 * @property source - 数据来源，目前仅支持"embedding-file"
 * @property file - 嵌入文件路径,目前仅适配source为embedding-file文件路径
 * @property query - 查询字符串,即检索词。一般取对话的最后一条消息内容(即: 用户输入的最后一个问题/内容)
 * @property threshold - 相似度阈值(0-1),由对话模板(*.cbt.md)的"response.retrievalAugmentation.threshold"节点控制。
 * @property maxResults - 最大返回结果数(正整数),由对话模板(*.cbt.md)的"response.retrievalAugmentation.maxResults"节点控制。
 *
 */
const ragSettingSchema = zod.object({
  variableName: zod.string(),
  type: zod.literal("similarity-search"), //TODO: 支持更多检索类型
  source: zod.literal("embedding-file"),  //TODO: 支持更多数据来源
  file: zod.string(),                     //TODO: 支持更多数据来源，目前仅适配source为embedding-file文件路径
  query: zod.string(),
  threshold: zod.number().min(0).max(1),
  maxResults: zod.number().int().min(1),
});
export type RAGSetting = zod.infer<typeof ragSettingSchema>;


/**
 * 定义对话类型模板中提示词配置结构及校验约定
 * @property placeholder - 可选占位文本
 * @property rag - 可选检索增强配置, Retrieval augmentation
 * @property tools - 可选工具列表
 * @property completionHandler - 可选完成处理器配置
 * @property LLM: maxTokens - 最大token数限制
 * @property LLM: temperature - 可选温度参数(控制生成随机性)
 * @property LLM: stop - 可选的停止词列表
 */
const conversationPromptSchema = zod.object({
  placeholder: zod.string().optional(),
  ragSetting: ragSettingSchema.optional(),
  tools: zod.array(zod.string()).optional(),
  completionHandler: completionHandlerSchema.optional(),
  // TODO 优化模板中的LLM配置定义方式
  llm: zod.object({
    provider: zod
      .enum(["ollama", "openai", "claude", "gemini", "deepseek"])
      .optional(),
    model: zod.string().optional(),
    maxTokens: zod.number().default(2048),
    temperature: zod.number().optional(),
    topP: zod.number().optional(),
    topK: zod.number().optional(),
    stop: zod.array(zod.string()).optional(),
  }),
});

export type ConversationPrompt = zod.infer<typeof conversationPromptSchema> & {
  /**
   * 对话模板中实际的提示模板，如：
   * - template-initial-message
   * - template-response
   */
  template: string;
};

const toolBaseSchema = zod.object({
  name: zod.string(),
  description: zod.string(),
  inputs: zod
    .array(
      zod.object({
        name: zod.string(),
        type: zod.enum(["string", "number", "boolean"]),
      })
    ).optional(),
});
const variableBaseSchema = zod.object({
  name: zod.string(),
  constraints: zod
    .array(
      zod.discriminatedUnion("type", [
        zod.object({
          type: zod.literal("text-length"),
          min: zod.number(),
        }),
      ])
    )
    .optional(),
});

/**
 * 定义对话类型板板的变量类型及校验约定
 * 包含以下模板变量类型：
 * - constant: 常量值，在会话开始时设置
 * 
 * - message: 消息内容，关联特定消息的属性
 * - context: 上下文信息，在会话开始时设置
 * 
 * - language: 语言类型，在会话开始时设置
 * 
 * - filename: 文件名，在会话开始时设置
 * - file-path: 文件路径，在会话开始时设置
 * - workspace-folder: 工作区文件夹，在会话开始时设置
 * - git-diff: Git差异，在会话开始时设置
 * 
 * - selected-text: 选中的文本，可在会话开始或消息时设置
 * - selected-location-text: 选中位置的文本，在会话开始时设置
 * - selected-text-with-diagnostics: 带诊断信息的选中文本，在会话开始时设置
 * 
 * 利用zod discriminatedUnion实现类型区分，通过"type"字段进行判别
 */
const variableSchema = zod.discriminatedUnion("type", [
  variableBaseSchema.extend({
    type: zod.literal("constant"),
    time: zod.literal("conversation-start"),
    value: zod.string(),
  }),
  variableBaseSchema.extend({
    type: zod.literal("message"),
    time: zod.literal("message"),
    index: zod.number(),
    property: zod.enum(["content"]),
  }),
  variableBaseSchema.extend({
    type: zod.literal("context"),
    time: zod.enum(["conversation-start"]),
  }),
  variableBaseSchema.extend({
    type: zod.literal("language"),
    time: zod.enum(["conversation-start"]),
  }),
  variableBaseSchema.extend({
    type: zod.literal("filename"),
    time: zod.enum(["conversation-start"]),
  }),
  variableBaseSchema.extend({
    type: zod.literal("file-path"),
    time: zod.enum(["conversation-start"]),
  }),
  variableBaseSchema.extend({
    type: zod.literal("workspace-folder"),
    time: zod.enum(["conversation-start"]),
  }),
  variableBaseSchema.extend({
    type: zod.literal("git-diff"),
    time: zod.enum(["conversation-start"]),
  }),
  // 摘选输入的内容
  variableBaseSchema.extend({
    type: zod.literal("selected-text"),
    time: zod.enum(["conversation-start", "message"]),
  }),
  variableBaseSchema.extend({
    type: zod.literal("selected-location-text"),
    time: zod.enum(["conversation-start"]),
  }),
  variableBaseSchema.extend({
    type: zod.literal("selected-text-with-diagnostics"),
    time: zod.enum(["conversation-start"]),
    severities: zod.array(zod.enum(["error", "warning", "information", "hint"])).optional(),
  }),
]);
export type Variable = zod.infer<typeof variableSchema>;


/**
 * 定义对话类型模板的对象数据结构及验证约定
 * 
 * @property id - 模板唯一标识符
 * @property engineVersion - 引擎版本号 (目前固定值: 0)
 * @property label - 模板显示名称
 * @property description - 模板描述
 * @property tags - 可选标签数组
 * @property header - 模板头部配置
 * @property header.title - 标题文本，默认模板中定义的标题值
 * @property header.useFirstMessageAsTitle - 是否使用首条消息作为标题 (默认 false)
 * @property header.icon - 图标配置
 * @property header.icon.type - 图标类型 (固定为 "codicon")
 * @property header.icon.value - 图标值
 * @property chatInterface - 聊天界面类型 (默认 "message-exchange")
 * @property isEnabled - 是否启用模板 (默认 true)
 * @property variables - 可选变量数组，即：输入对话的内容，例如：选中文本/代码，第一条消息内容、最后一条消息内容等。
 * @property initialMessage - 可选初始消息提示
 * @property response - 响应消息提示
 */
export const conversationTemplateSchema = zod.object({
  id: zod.string(),
  engineVersion: zod.literal(0),  //TODO: 支持多版本引擎, 关联 conversationEngineVersion及PersistedChatState.version
  label: zod.string(),
  description: zod.string(),
  tags: zod.array(zod.string()).optional(),
  header: zod.object({
    title: zod.string(),
    useFirstMessageAsTitle: zod.boolean().optional(), // default: false
    icon: zod.object({
      type: zod.literal("codicon"),
      value: zod.string(),
    }),
  }),
  chatInterface: zod
    .enum(["message-exchange", "instruction-refinement"])
    .optional(), // default: message-exchange
  isEnabled: zod.boolean().optional(), // default: true
  variables: zod.array(variableSchema).optional(), // 
  initialMessage: conversationPromptSchema.optional(),
  response: conversationPromptSchema,
});
export type ConversationTemplate = zod.infer<typeof conversationTemplateSchema> & {
  initialMessage?: ConversationPrompt;
  response: ConversationPrompt;
};
