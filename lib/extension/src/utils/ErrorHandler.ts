import * as vscode from 'vscode';

export enum ErrorCategory {
  CONFIGURATION = "configuration",
  NETWORK = "network",
  AI_SERVICE = "ai_service",
  FILE_SYSTEM = "file_system",
  USER_INPUT = "user_input",
  INTERNAL = "internal",
  AUTHENTICATION = "authentication",
  PERFORMANCE = "performance"
}

export enum ErrorSeverity {
  LOW = "low",
  MEDIUM = "medium",
  HIGH = "high",
  CRITICAL = "critical"
}

export interface ErrorReport {
  id: string;
  timestamp: number;
  context: string;
  message: string;
  category: ErrorCategory;
  severity: ErrorSeverity;
  metadata: Record<string, unknown>;
  stack?: string;
}

/**
 * Enhanced centralized error handling utility for the extension
 */
export class ErrorHandler {
  private static outputChannel: vscode.OutputChannel | undefined;
  private static errorHistory: ErrorReport[] = [];
  private static readonly MAX_ERROR_HISTORY = 100;
  private static errorCounts: Map<string, number> = new Map();

  /**
   * Initialize the error handler with an output channel
   */
  static initialize(outputChannel: vscode.OutputChannel) {
    this.outputChannel = outputChannel;
  }

  /**
   * Handle errors with consistent logging and user notification
   */
  static async handleError(
    error: unknown,
    context: string,
    options: {
      showToUser?: boolean;
      logLevel?: 'error' | 'warn' | 'info';
      userMessage?: string;
      category?: ErrorCategory;
      severity?: ErrorSeverity;
      metadata?: Record<string, unknown>;
    } = {}
  ): Promise<void> {
    const {
      showToUser = true,
      logLevel = 'error',
      userMessage,
      category = ErrorCategory.INTERNAL,
      severity = ErrorSeverity.MEDIUM,
      metadata = {}
    } = options;

    const errorMessage = this.formatError(error);
    const errorId = this.generateErrorId(context, errorMessage);

    // Create error report
    const report: ErrorReport = {
      id: errorId,
      timestamp: Date.now(),
      context,
      message: errorMessage,
      category,
      severity,
      metadata,
      stack: error instanceof Error ? error.stack : undefined
    };

    // Track error frequency
    const errorKey = `${category}:${context}`;
    this.errorCounts.set(errorKey, (this.errorCounts.get(errorKey) || 0) + 1);

    // Store in history
    this.addToHistory(report);

    const fullMessage = `[${category.toUpperCase()}] ${context}: ${errorMessage}`;

    // Log to output channel
    if (this.outputChannel) {
      const timestamp = new Date().toISOString();
      this.outputChannel.appendLine(`${timestamp} [${logLevel.toUpperCase()}] ${fullMessage}`);

      if (logLevel === 'error') {
        this.outputChannel.show(true);
      }
    }

    // Log to console for development
    console.error(fullMessage, error);

    // Show to user if requested
    if (showToUser) {
      const displayMessage = userMessage || `Error in ${context}: ${errorMessage}`;

      switch (logLevel) {
        case 'error':
          await vscode.window.showErrorMessage(displayMessage);
          break;
        case 'warn':
          await vscode.window.showWarningMessage(displayMessage);
          break;
        case 'info':
          await vscode.window.showInformationMessage(displayMessage);
          break;
      }
    }
  }

  /**
   * Format error into a readable string
   */
  private static formatError(error: unknown): string {
    if (error instanceof Error) {
      return error.message;
    }

    if (typeof error === 'string') {
      return error;
    }

    if (error && typeof error === 'object') {
      try {
        return JSON.stringify(error);
      } catch {
        return String(error);
      }
    }

    return 'Unknown error occurred';
  }

  /**
   * Get error statistics for monitoring
   */
  static getErrorStats(): {
    totalErrors: number;
    errorsByCategory: Record<ErrorCategory, number>;
    errorsBySeverity: Record<ErrorSeverity, number>;
    frequentErrors: Array<{ key: string; count: number }>;
    recentErrors: ErrorReport[];
  } {
    const errorsByCategory = {} as Record<ErrorCategory, number>;
    const errorsBySeverity = {} as Record<ErrorSeverity, number>;

    // Initialize counters
    Object.values(ErrorCategory).forEach(cat => errorsByCategory[cat] = 0);
    Object.values(ErrorSeverity).forEach(sev => errorsBySeverity[sev] = 0);

    // Count errors
    this.errorHistory.forEach(error => {
      errorsByCategory[error.category]++;
      errorsBySeverity[error.severity]++;
    });

    // Get frequent errors
    const frequentErrors = Array.from(this.errorCounts.entries())
      .map(([key, count]) => ({ key, count }))
      .sort((a, b) => b.count - a.count)
      .slice(0, 10);

    return {
      totalErrors: this.errorHistory.length,
      errorsByCategory,
      errorsBySeverity,
      frequentErrors,
      recentErrors: this.errorHistory.slice(-10)
    };
  }

  /**
   * Clear error history (useful for testing)
   */
  static clearHistory(): void {
    this.errorHistory = [];
    this.errorCounts.clear();
  }

  /**
   * Generate unique error ID
   */
  private static generateErrorId(context: string, message: string): string {
    const hash = this.simpleHash(`${context}:${message}:${Date.now()}`);
    return `err_${hash}`;
  }

  /**
   * Add error to history with size limit
   */
  private static addToHistory(report: ErrorReport): void {
    this.errorHistory.push(report);
    if (this.errorHistory.length > this.MAX_ERROR_HISTORY) {
      this.errorHistory.shift();
    }
  }

  /**
   * Simple hash function for error IDs
   */
  private static simpleHash(str: string): string {
    let hash = 0;
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32-bit integer
    }
    return Math.abs(hash).toString(36);
  }

  /**
   * Create a safe async wrapper that handles errors
   */
  static wrapAsync<T extends unknown[], R>(
    fn: (...args: T) => Promise<R>,
    context: string,
    options?: {
      showToUser?: boolean;
      userMessage?: string;
      fallbackValue?: R;
    }
  ) {
    return async (...args: T): Promise<R | undefined> => {
      try {
        return await fn(...args);
      } catch (error) {
        await this.handleError(error, context, {
          showToUser: options?.showToUser,
          userMessage: options?.userMessage,
        });
        return options?.fallbackValue;
      }
    };
  }

  /**
   * Create a safe sync wrapper that handles errors
   */
  static wrapSync<T extends unknown[], R>(
    fn: (...args: T) => R,
    context: string,
    options?: {
      showToUser?: boolean;
      userMessage?: string;
      fallbackValue?: R;
    }
  ) {
    return (...args: T): R | undefined => {
      try {
        return fn(...args);
      } catch (error) {
        // Handle async error handling in background
        this.handleError(error, context, {
          showToUser: options?.showToUser,
          userMessage: options?.userMessage,
        });
        return options?.fallbackValue;
      }
    };
  }
}

/**
 * Decorator for automatic error handling in class methods
 */
export function handleErrors(
  context: string,
  options?: {
    showToUser?: boolean;
    userMessage?: string;
  }
) {
  return function <T extends Record<string, unknown>>(
    target: T,
    propertyKey: string,
    descriptor: PropertyDescriptor
  ) {
    const originalMethod = descriptor.value;

    descriptor.value = async function (...args: unknown[]) {
      try {
        return await originalMethod.apply(this, args);
      } catch (error) {
        await ErrorHandler.handleError(error, `${target.constructor.name}.${propertyKey}`, {
          showToUser: options?.showToUser,
          userMessage: options?.userMessage,
        });
        throw error; // Re-throw to maintain original behavior
      }
    };

    return descriptor;
  };
}
