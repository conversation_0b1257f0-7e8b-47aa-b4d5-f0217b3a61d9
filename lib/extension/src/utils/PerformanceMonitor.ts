import * as vscode from 'vscode';

/**
 * Performance metrics interface
 */
export interface PerformanceMetric {
  name: string;
  duration: number;
  timestamp: number;
  metadata?: Record<string, unknown>;
}

/**
 * Performance monitoring utility for tracking operation performance
 */
export class PerformanceMonitor {
  private static metrics: PerformanceMetric[] = [];
  private static outputChannel: vscode.OutputChannel | undefined;
  private static maxMetrics = 1000; // Keep last 1000 metrics
  private static thresholds: Map<string, { warning: number; critical: number }> = new Map();
  private static cacheStats: Map<string, { hits: number; misses: number }> = new Map();

  /**
   * Initialize the performance monitor
   */
  static initialize(outputChannel: vscode.OutputChannel) {
    this.outputChannel = outputChannel;

    // Set default thresholds
    this.setThreshold('ai.generateText', 5000, 10000); // 5s warning, 10s critical
    this.setThreshold('ai.generateEmbedding', 3000, 6000); // 3s warning, 6s critical
    this.setThreshold('file.read', 1000, 3000); // 1s warning, 3s critical
    this.setThreshold('file.write', 2000, 5000); // 2s warning, 5s critical
  }

  /**
   * Set performance thresholds for operations
   */
  static setThreshold(operationName: string, warning: number, critical: number): void {
    this.thresholds.set(operationName, { warning, critical });
  }

  /**
   * Record cache operation for efficiency tracking
   */
  static recordCacheOperation(cacheName: string, hit: boolean): void {
    const stats = this.cacheStats.get(cacheName) || { hits: 0, misses: 0 };

    if (hit) {
      stats.hits++;
    } else {
      stats.misses++;
    }

    this.cacheStats.set(cacheName, stats);
  }

  /**
   * Start timing an operation
   */
  static startTimer(name: string): (metadata?: Record<string, unknown>) => void {
    const startTime = performance.now();

    return (metadata?: Record<string, unknown>) => {
      const endTime = performance.now();
      const duration = endTime - startTime;

      this.recordMetric({
        name,
        duration,
        timestamp: Date.now(),
        metadata,
      });
    };
  }

  /**
   * Time an async operation
   */
  static async timeAsync<T>(
    name: string,
    operation: () => Promise<T>,
    metadata?: Record<string, unknown>
  ): Promise<T> {
    const stopTimer = this.startTimer(name);

    try {
      const result = await operation();
      stopTimer(metadata);
      return result;
    } catch (error) {
      stopTimer({ ...metadata, error: true });
      throw error;
    }
  }

  /**
   * Time a sync operation
   */
  static timeSync<T>(
    name: string,
    operation: () => T,
    metadata?: Record<string, unknown>
  ): T {
    const stopTimer = this.startTimer(name);

    try {
      const result = operation();
      stopTimer(metadata);
      return result;
    } catch (error) {
      stopTimer({ ...metadata, error: true });
      throw error;
    }
  }

  /**
   * Record a performance metric
   */
  private static recordMetric(metric: PerformanceMetric) {
    this.metrics.push(metric);

    // Keep only the last N metrics to prevent memory leaks
    // Use more efficient cleanup when threshold is exceeded
    if (this.metrics.length > this.maxMetrics) {
      const keepCount = Math.floor(this.maxMetrics * 0.8); // Keep 80% of max
      this.metrics = this.metrics.slice(-keepCount);
    }

    // Check thresholds and log accordingly
    this.checkThresholds(metric.name, metric.duration);

    // Log slow operations
    if (metric.duration > 1000) { // > 1 second
      this.logSlowOperation(metric);
    }
  }

  /**
   * Log slow operations to output channel
   */
  private static logSlowOperation(metric: PerformanceMetric) {
    if (this.outputChannel) {
      const timestamp = new Date(metric.timestamp).toISOString();
      const metadataStr = metric.metadata ? JSON.stringify(metric.metadata) : '';

      this.outputChannel.appendLine(
        `[PERFORMANCE] ${timestamp} - Slow operation: ${metric.name} took ${metric.duration.toFixed(2)}ms ${metadataStr}`
      );
    }
  }

  /**
   * Get performance statistics
   */
  static getStats(operationName?: string): {
    count: number;
    averageDuration: number;
    minDuration: number;
    maxDuration: number;
    totalDuration: number;
  } {
    const filteredMetrics = operationName
      ? this.metrics.filter(m => m.name === operationName)
      : this.metrics;

    if (filteredMetrics.length === 0) {
      return {
        count: 0,
        averageDuration: 0,
        minDuration: 0,
        maxDuration: 0,
        totalDuration: 0,
      };
    }

    const durations = filteredMetrics.map(m => m.duration);
    const totalDuration = durations.reduce((sum, d) => sum + d, 0);

    return {
      count: filteredMetrics.length,
      averageDuration: totalDuration / filteredMetrics.length,
      minDuration: Math.min(...durations),
      maxDuration: Math.max(...durations),
      totalDuration,
    };
  }

  /**
   * Get recent metrics (last N minutes)
   */
  static getRecentMetrics(minutes: number = 5): PerformanceMetric[] {
    const cutoff = Date.now() - (minutes * 60 * 1000);
    return this.metrics.filter(m => m.timestamp >= cutoff);
  }

  /**
   * Clear all metrics
   */
  static clearMetrics() {
    this.metrics = [];
  }

  /**
   * Export metrics for analysis
   */
  static exportMetrics(): PerformanceMetric[] {
    return [...this.metrics];
  }

  /**
   * Show performance report in VS Code
   */
  static async showPerformanceReport() {
    const stats = this.getStats();
    const recentMetrics = this.getRecentMetrics(5);

    // Group recent metrics by operation name
    const operationStats = new Map<string, PerformanceMetric[]>();
    recentMetrics.forEach(metric => {
      if (!operationStats.has(metric.name)) {
        operationStats.set(metric.name, []);
      }
      operationStats.get(metric.name)!.push(metric);
    });

    let report = `Performance Report (Last 5 minutes)\n`;
    report += `=====================================\n\n`;
    report += `Overall Stats:\n`;
    report += `- Total operations: ${stats.count}\n`;
    report += `- Average duration: ${stats.averageDuration.toFixed(2)}ms\n`;
    report += `- Min duration: ${stats.minDuration.toFixed(2)}ms\n`;
    report += `- Max duration: ${stats.maxDuration.toFixed(2)}ms\n\n`;

    if (operationStats.size > 0) {
      report += `Operations:\n`;
      for (const [name, metrics] of operationStats) {
        const opStats = this.getStats(name);
        report += `- ${name}: ${metrics.length} calls, avg ${opStats.averageDuration.toFixed(2)}ms\n`;
      }
    }

    // Show in a new document
    const doc = await vscode.workspace.openTextDocument({
      content: report,
      language: 'plaintext',
    });
    await vscode.window.showTextDocument(doc);
  }
}

/**
 * Decorator for automatic performance monitoring
 */
export function monitor(operationName?: string) {
  return function <T extends Record<string, unknown>>(
    target: T,
    propertyKey: string,
    descriptor: PropertyDescriptor
  ) {
    const originalMethod = descriptor.value;
    const name = operationName || `${target.constructor.name}.${propertyKey}`;

    descriptor.value = async function (...args: unknown[]) {
      return await PerformanceMonitor.timeAsync(
        name,
        () => originalMethod.apply(this, args),
        { className: target.constructor.name, methodName: propertyKey }
      );
    };

    return descriptor;
  };
}

/**
 * Enhanced performance monitoring utilities
 */
export class PerformanceUtils {
  /**
   * Get cache efficiency statistics
   */
  static getCacheEfficiency(): Record<string, { hitRate: number; efficiency: string }> {
    const efficiency: Record<string, { hitRate: number; efficiency: string }> = {};

    PerformanceMonitor['cacheStats'].forEach((stats, cacheName) => {
      const totalOps = stats.hits + stats.misses;
      const hitRate = totalOps > 0 ? stats.hits / totalOps : 0;

      let efficiencyLabel = 'Unknown';
      if (hitRate >= 0.9) efficiencyLabel = 'Excellent';
      else if (hitRate >= 0.7) efficiencyLabel = 'Good';
      else if (hitRate >= 0.5) efficiencyLabel = 'Fair';
      else efficiencyLabel = 'Poor';

      efficiency[cacheName] = { hitRate, efficiency: efficiencyLabel };
    });

    return efficiency;
  }

  /**
   * Get memory usage if available
   */
  static getMemoryUsage(): { heapUsed: number; heapTotal: number; external: number } | null {
    if (typeof process !== 'undefined' && process.memoryUsage) {
      const usage = process.memoryUsage();
      return {
        heapUsed: usage.heapUsed,
        heapTotal: usage.heapTotal,
        external: usage.external
      };
    }
    return null;
  }

  /**
   * Format memory size for display
   */
  static formatMemorySize(bytes: number): string {
    const mb = bytes / (1024 * 1024);
    return `${mb.toFixed(2)}MB`;
  }
}

// Add private method to PerformanceMonitor
declare module './PerformanceMonitor' {
  namespace PerformanceMonitor {
    function checkThresholds(operationName: string, duration: number): void;
  }
}

// Extend PerformanceMonitor with threshold checking
(PerformanceMonitor as any).checkThresholds = function (operationName: string, duration: number): void {
  const threshold = PerformanceMonitor['thresholds'].get(operationName);
  if (!threshold) return;

  if (duration > threshold.critical) {
    console.error(`🚨 CRITICAL: Operation '${operationName}' took ${duration.toFixed(2)}ms (threshold: ${threshold.critical}ms)`);
    if (PerformanceMonitor['outputChannel']) {
      PerformanceMonitor['outputChannel'].appendLine(
        `[CRITICAL] ${new Date().toISOString()} - Operation '${operationName}' exceeded critical threshold: ${duration.toFixed(2)}ms > ${threshold.critical}ms`
      );
    }
  } else if (duration > threshold.warning) {
    console.warn(`⚠️ WARNING: Operation '${operationName}' took ${duration.toFixed(2)}ms (threshold: ${threshold.warning}ms)`);
    if (PerformanceMonitor['outputChannel']) {
      PerformanceMonitor['outputChannel'].appendLine(
        `[WARNING] ${new Date().toISOString()} - Operation '${operationName}' exceeded warning threshold: ${duration.toFixed(2)}ms > ${threshold.warning}ms`
      );
    }
  }
};
