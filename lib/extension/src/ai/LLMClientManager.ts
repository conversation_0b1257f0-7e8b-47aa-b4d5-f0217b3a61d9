/**
 * Main LLM Client Manager with Template Integration
 */

import { Logger } from "../logger";
import { APIKeyManager } from "./ApiKeyManager";
import { ILLMClientManager, ILLMProvider } from "./interfaces";
import { LLMClientFactory } from "./LLMClientFactory";
import { OpenAIProvider } from "./providers/OpenAIProvider";
import { OllamaProvider } from "./providers/OllamaProvider";
import {
  StandardizedLLMRequest,
  StandardizedLLMResponse,
  ProviderConfig,
  ProviderHealth,
  ModelInfo,
  TemplateContext
} from "./types";

export class LLMClientManager implements ILLMClientManager {
  private factory: LLMClientFactory;
  private templateCache = new Map<string, TemplateContext>();
  private logger: Logger;
  private apiKeyManager: APIKeyManager;
  
  constructor(logger: Logger, apiKeyManager: APIKeyManager) {
    this.logger = logger;
    this.apiKeyManager = apiKeyManager;
    this.factory = new LLMClientFactory(logger);
    this.initializeProviders();
  }
  
  private async initializeProviders(): Promise<void> {
    // Register built-in providers
    const openaiProvider = new OpenAIProvider(this.apiKeyManager, this.logger);
    const ollamaProvider = new OllamaProvider(this.logger);
    
    this.factory.registerProvider(openaiProvider);
    this.factory.registerProvider(ollamaProvider);
    
    // Discover additional providers from VSCode extensions
    await this.factory.discoverProviders();
    
    this.logger.log('🏭 LLM Client Manager initialized with factory pattern');
  }
  
  // Template-based client creation
  async createClientFromTemplate(templatePath: string): Promise<ILLMProvider> {
    const templateContext = await this.parseTemplate(templatePath);
    this.templateCache.set(templatePath, templateContext);
    
    return this.factory.createClientForTemplate(templatePath);
  }
  
  // Model-based client creation
  async createClientForModel(model: string, config?: ProviderConfig): Promise<ILLMProvider> {
    return this.factory.createClientForModel(model, config);
  }
  
  // Provider-based client creation
  async createClient(providerId: string, config?: ProviderConfig): Promise<ILLMProvider> {
    return this.factory.createClient(providerId, config);
  }
  
  // Standardized LLM operations
  async generateText(request: StandardizedLLMRequest): Promise<StandardizedLLMResponse> {
    const client = await this.selectBestProvider(request);
    return client.generateText(request);
  }
  
  async generateTextStream(request: StandardizedLLMRequest): Promise<AsyncIterable<StandardizedLLMResponse>> {
    const client = await this.selectBestProvider(request);
    return client.generateTextStream(request);
  }
  
  async generateEmbedding(request: StandardizedLLMRequest): Promise<StandardizedLLMResponse> {
    // Only OpenAI supports embeddings currently
    const client = await this.factory.createClient('openai');
    return client.generateEmbedding(request);
  }
  
  async generateImage(request: StandardizedLLMRequest): Promise<StandardizedLLMResponse> {
    // Only OpenAI supports image generation currently
    const client = await this.factory.createClient('openai');
    return client.generateImage(request);
  }
  
  // Health monitoring
  async checkProviderHealth(): Promise<Record<string, ProviderHealth>> {
    return this.factory.checkAllProviders();
  }
  
  // Provider discovery
  async getAvailableProviders(): Promise<string[]> {
    return this.factory.getAvailableProviders();
  }
  
  async getProviderInfo(providerId: string): Promise<ModelInfo[]> {
    return this.factory.getProviderInfo(providerId);
  }
  
  // Template management
  async parseTemplate(templatePath: string): Promise<TemplateContext> {
    // This would parse .cbt.md files to extract:
    // - Provider preferences
    // - Model requirements
    // - Custom settings
    // - Variables and metadata
    
    // For now, return a basic template context
    return {
      templateId: templatePath,
      templatePath,
      variables: {},
      metadata: {}
    };
  }
  
  getTemplateContext(templatePath: string): TemplateContext | undefined {
    return this.templateCache.get(templatePath);
  }
  
  // Smart provider selection
  private async selectBestProvider(request: StandardizedLLMRequest): Promise<ILLMProvider> {
    // If model is specified, find provider that supports it
    if (request.model) {
      try {
        return await this.factory.createClientForModel(request.model);
      } catch (error) {
        this.logger.log(`⚠️ Failed to find provider for model ${request.model}: ${error}`);
      }
    }
    
    // If template context is provided, use template-based selection
    if (request.templateContext?.templatePath) {
      try {
        return await this.factory.createClientForTemplate(request.templateContext.templatePath);
      } catch (error) {
        this.logger.log(`⚠️ Failed to create client from template: ${error}`);
      }
    }
    
    // Fallback to first available provider
    const availableProviders = await this.factory.getAvailableProviders();
    if (availableProviders.length === 0) {
      throw new Error('No available LLM providers');
    }
    
    const firstProvider = availableProviders[0];
    if (!firstProvider) {
      throw new Error('No available LLM providers');
    }
    
    return this.factory.createClient(firstProvider);
  }
}
