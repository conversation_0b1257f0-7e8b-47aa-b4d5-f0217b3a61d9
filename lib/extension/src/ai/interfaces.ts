/**
 * Standardized LLM Provider Interfaces
 */

import {
  StandardizedLLMRequest,
  StandardizedLLMResponse,
  ProviderCapabilities,
  ProviderConfig,
  ProviderHealth,
  ModelInfo,
  MCPContext,
  RAGContext,
  RAGDocument
} from './types';

// ============================================================================
// Core Provider Interface
// ============================================================================

export interface ILLMProvider {
  readonly id: string;
  readonly name: string;
  readonly version: string;
  readonly capabilities: ProviderCapabilities;
  
  // Provider lifecycle
  initialize(config: ProviderConfig): Promise<void>;
  dispose(): Promise<void>;
  
  // Health and availability
  isAvailable(): Promise<boolean>;
  getHealth(): Promise<ProviderHealth>;
  
  // Model support
  getSupportedModels(): Promise<ModelInfo[]>;
  supportsModel(model: string): boolean;
  
  // Core LLM operations
  generateText(request: StandardizedLLMRequest): Promise<StandardizedLLMResponse>;
  generateTextStream(request: StandardizedLLMRequest): AsyncIterable<StandardizedLLMResponse>;
  generateEmbedding(request: StandardizedLLMRequest): Promise<StandardizedLLMResponse>;
  generateImage(request: StandardizedLLMRequest): Promise<StandardizedLLMResponse>;
  
  // Advanced capabilities
  callTool(request: StandardizedLLMRequest): Promise<StandardizedLLMResponse>;
  callFunction(request: StandardizedLLMRequest): Promise<StandardizedLLMResponse>;
  
  // MCP integration
  connectMCP(context: MCPContext): Promise<void>;
  disconnectMCP(): Promise<void>;
  
  // RAG integration
  setupRAG(context: RAGContext): Promise<void>;
  queryRAG(query: string): Promise<RAGDocument[]>;
}

// ============================================================================
// Factory Interface
// ============================================================================

export interface ILLMClientFactory {
  // Provider registration and discovery
  registerProvider(provider: ILLMProvider): void;
  unregisterProvider(providerId: string): void;
  discoverProviders(): Promise<ILLMProvider[]>;
  
  // Client creation
  createClient(providerId: string, config?: ProviderConfig): Promise<ILLMProvider>;
  createClientForModel(model: string, config?: ProviderConfig): Promise<ILLMProvider>;
  createClientForTemplate(templatePath: string): Promise<ILLMProvider>;
  
  // Provider management
  getAvailableProviders(): Promise<string[]>;
  getProviderInfo(providerId: string): Promise<ModelInfo[]>;
  
  // Health monitoring
  checkAllProviders(): Promise<Record<string, ProviderHealth>>;
}

// ============================================================================
// Client Manager Interface
// ============================================================================

export interface ILLMClientManager {
  // Template-based client creation
  createClientFromTemplate(templatePath: string): Promise<ILLMProvider>;
  
  // Model-based client creation
  createClientForModel(model: string, config?: ProviderConfig): Promise<ILLMProvider>;
  
  // Provider-based client creation
  createClient(providerId: string, config?: ProviderConfig): Promise<ILLMProvider>;
  
  // Standardized LLM operations
  generateText(request: StandardizedLLMRequest): Promise<StandardizedLLMResponse>;
  generateTextStream(request: StandardizedLLMRequest): Promise<AsyncIterable<StandardizedLLMResponse>>;
  generateEmbedding(request: StandardizedLLMRequest): Promise<StandardizedLLMResponse>;
  generateImage(request: StandardizedLLMRequest): Promise<StandardizedLLMResponse>;
  
  // Health monitoring
  checkProviderHealth(): Promise<Record<string, ProviderHealth>>;
  
  // Provider discovery
  getAvailableProviders(): Promise<string[]>;
  getProviderInfo(providerId: string): Promise<ModelInfo[]>;
}

// ============================================================================
// Legacy Compatibility Interface
// ============================================================================

export interface Provider {
  id: string;
  name: string;
  isAvailable(): Promise<boolean>;
  supportsModel(model: string): boolean;
}
