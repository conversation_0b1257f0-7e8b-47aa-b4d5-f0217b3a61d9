/**
 * Standardized Type Definitions for LLM Client Integration System
 */

export type Vector = number[];

// ============================================================================
// Core Request/Response Types
// ============================================================================

export interface StandardizedLLMRequest {
  // Core inference parameters
  prompt: string;
  model?: string;
  maxTokens?: number;
  temperature?: number;
  topP?: number;
  stop?: string[];
  stream?: boolean;
  
  // Tool and function integration
  tools?: ToolDefinition[];
  toolChoice?: 'auto' | 'none' | string;
  functions?: FunctionDefinition[];
  functionCall?: 'auto' | 'none' | string;
  
  // MCP (Model Context Protocol) integration
  mcpContext?: MCPContext;
  
  // RAG integration
  ragContext?: RAGContext;
  
  // Provider-specific custom settings
  providerSettings?: Record<string, unknown>;
  
  // Conversation template context
  templateContext?: TemplateContext;
}

export interface StandardizedLLMResponse {
  type: 'text' | 'image' | 'embedding' | 'tool_call' | 'error';
  
  // Text generation and completion
  content?: string;
  finishReason?: 'stop' | 'length' | 'tool_calls' | 'content_filter';
  
  // Image generation and recognition
  imageUrl?: string;
  imageData?: string;
  imageAnalysis?: string;
  
  // Embeddings and vector operations
  embedding?: Vector;
  similarity?: number;
  
  // Tool and function calls
  toolCalls?: ToolCall[];
  functionCall?: FunctionCall;
  
  // Metadata
  usage?: {
    promptTokens: number;
    completionTokens: number;
    totalTokens: number;
  };
  model?: string;
  provider?: string;
  
  // Error information
  error?: string;
  errorCode?: string;
}

// ============================================================================
// Tool and Function Types
// ============================================================================

export interface ToolDefinition {
  type: 'function';
  function: {
    name: string;
    description: string;
    parameters: Record<string, unknown>;
  };
}

export interface FunctionDefinition {
  name: string;
  description: string;
  parameters: Record<string, unknown>;
}

export interface ToolCall {
  id: string;
  type: 'function';
  function: {
    name: string;
    arguments: string;
  };
}

export interface FunctionCall {
  name: string;
  arguments: string;
}

// ============================================================================
// MCP (Model Context Protocol) Types
// ============================================================================

export interface MCPContext {
  servers?: MCPServer[];
  resources?: MCPResource[];
  tools?: MCPTool[];
}

export interface MCPServer {
  name: string;
  command: string;
  args?: string[];
  env?: Record<string, string>;
}

export interface MCPResource {
  uri: string;
  name: string;
  description?: string;
  mimeType?: string;
}

export interface MCPTool {
  name: string;
  description: string;
  inputSchema: Record<string, unknown>;
}

// ============================================================================
// RAG Integration Types
// ============================================================================

export interface RAGContext {
  documents?: RAGDocument[];
  vectorStore?: string;
  retrievalSettings?: {
    topK?: number;
    threshold?: number;
    rerank?: boolean;
  };
}

export interface RAGDocument {
  id: string;
  content: string;
  metadata?: Record<string, unknown>;
  embedding?: Vector;
}

// ============================================================================
// Template Integration Types
// ============================================================================

export interface TemplateContext {
  templateId?: string;
  templatePath?: string;
  variables?: Record<string, unknown>;
  metadata?: Record<string, unknown>;
}

// ============================================================================
// Provider Types
// ============================================================================

export interface ProviderCapabilities {
  textGeneration: boolean;
  textCompletion: boolean;
  imageGeneration: boolean;
  imageRecognition: boolean;
  embedding: boolean;
  toolCalling: boolean;
  functionCalling: boolean;
  streaming: boolean;
  mcpSupport: boolean;
  ragSupport: boolean;
  customSettings: string[];
}

export interface ProviderConfig {
  apiKey?: string;
  baseUrl?: string;
  model?: string;
  timeout?: number;
  retries?: number;
  customSettings?: Record<string, unknown>;
}

export interface ProviderHealth {
  status: 'healthy' | 'degraded' | 'unhealthy';
  latency?: number;
  errorRate?: number;
  lastCheck: Date;
  details?: string;
}

export interface ModelInfo {
  id: string;
  name: string;
  description?: string;
  contextLength?: number;
  capabilities: string[];
  pricing?: {
    input: number;
    output: number;
    currency: string;
  };
}

// ============================================================================
// Legacy Compatibility Types
// ============================================================================

export interface ChatRequest {
  prompt: string;
  maxTokens: number;
  stop?: string[];
  temperature?: number;
}

export interface EmbeddingResult {
  type: "success" | "error";
  embedding?: Vector;
  totalTokenCount?: number;
  errorMessage?: string;
}
