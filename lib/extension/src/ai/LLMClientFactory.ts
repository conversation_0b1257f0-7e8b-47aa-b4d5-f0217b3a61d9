/**
 * LLM Client Factory Implementation
 */

import { Logger } from "../logger";
import { ILLMClientFactory, ILLMProvider } from "./interfaces";
import { ProviderConfig, ProviderHealth, ModelInfo, TemplateContext } from "./types";

export class LLMClientFactory implements ILLMClientFactory {
  private providers = new Map<string, ILLMProvider>();
  private logger: Logger;
  
  constructor(logger: Logger) {
    this.logger = logger;
  }
  
  registerProvider(provider: ILLMProvider): void {
    this.providers.set(provider.id, provider);
    this.logger.log(`📦 Registered LLM provider: ${provider.name} (${provider.id})`);
  }
  
  unregisterProvider(providerId: string): void {
    const provider = this.providers.get(providerId);
    if (provider) {
      provider.dispose();
      this.providers.delete(providerId);
      this.logger.log(`🗑️ Unregistered LLM provider: ${providerId}`);
    }
  }
  
  async discoverProviders(): Promise<ILLMProvider[]> {
    // Discover VSCode extension providers
    const extensionProviders = await this.discoverExtensionProviders();
    
    // Register discovered providers
    for (const provider of extensionProviders) {
      this.registerProvider(provider);
    }
    
    return Array.from(this.providers.values());
  }
  
  async createClient(providerId: string, config?: ProviderConfig): Promise<ILLMProvider> {
    const provider = this.providers.get(providerId);
    if (!provider) {
      throw new Error(`Provider not found: ${providerId}`);
    }
    
    if (config) {
      await provider.initialize(config);
    }
    
    return provider;
  }
  
  async createClientForModel(model: string, config?: ProviderConfig): Promise<ILLMProvider> {
    // Find provider that supports the model
    for (const provider of this.providers.values()) {
      if (provider.supportsModel(model) && await provider.isAvailable()) {
        if (config) {
          await provider.initialize(config);
        }
        return provider;
      }
    }
    
    throw new Error(`No available provider for model: ${model}`);
  }
  
  async createClientForTemplate(templatePath: string): Promise<ILLMProvider> {
    // Parse template to extract provider requirements
    const templateConfig = await this.parseTemplateConfig(templatePath);
    
    if (templateConfig.providerId) {
      return this.createClient(templateConfig.providerId, templateConfig.config);
    } else if (templateConfig.model) {
      return this.createClientForModel(templateConfig.model, templateConfig.config);
    }
    
    // Use default provider
    const providers = Array.from(this.providers.values());
    const availableProvider = providers.find(p => p.isAvailable());
    if (!availableProvider) {
      throw new Error('No available providers for template');
    }
    
    return availableProvider;
  }
  
  async getAvailableProviders(): Promise<string[]> {
    const available: string[] = [];
    for (const [id, provider] of this.providers) {
      if (await provider.isAvailable()) {
        available.push(id);
      }
    }
    return available;
  }
  
  async getProviderInfo(providerId: string): Promise<ModelInfo[]> {
    const provider = this.providers.get(providerId);
    if (!provider) {
      throw new Error(`Provider not found: ${providerId}`);
    }
    
    return provider.getSupportedModels();
  }
  
  async checkAllProviders(): Promise<Record<string, ProviderHealth>> {
    const health: Record<string, ProviderHealth> = {};
    
    for (const [id, provider] of this.providers) {
      try {
        health[id] = await provider.getHealth();
      } catch (error) {
        health[id] = {
          status: 'unhealthy',
          lastCheck: new Date(),
          details: error instanceof Error ? error.message : 'Unknown error'
        };
      }
    }
    
    return health;
  }
  
  private async discoverExtensionProviders(): Promise<ILLMProvider[]> {
    // VSCode extension discovery implementation
    // This would scan for extensions that provide LLM capabilities
    return [];
  }
  
  private async parseTemplateConfig(_templatePath: string): Promise<{
    providerId?: string;
    model?: string;
    config?: ProviderConfig;
  }> {
    // Parse .cbt.md template file to extract provider configuration
    // This would read the template file and extract metadata
    return {};
  }
}
