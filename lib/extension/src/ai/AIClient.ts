/**
 * Modern LLM Client Integration System - Main Entry Point
 *
 * This is now a clean, focused entry point that delegates to the modular system:
 * 1. Standardized integration layer with VSCode extension best practices
 * 2. Factory pattern for LLM client management
 * 3. Conversation template system integration
 * 4. Extensible provider discovery and integration
 * 5. Standardized request/response handling for all LLM capabilities
 *
 * The heavy lifting is now done by specialized modules.
 */

import { ChatOllama } from "@langchain/ollama";
import { ChatOpenAI, OpenAIEmbeddings } from "@langchain/openai";
import { ConfigurationManager, ConfigUtils } from "../config/ConfigurationManager";
import { Logger } from "../logger";
import { ErrorHandler } from "../utils/ErrorHandler";
import { APIKeyManager } from "./ApiKeyManager";
import { LLMClientManager } from "./LLMClientManager";

// Re-export types for backward compatibility
export type { Provider } from "./interfaces";
export type {
  ChatRequest,
  EmbeddingResult,
  StandardizedLLMRequest,
  StandardizedLLMResponse
} from "./types";

// Simple provider interface for legacy compatibility
interface SimpleProvider {
  id: string;
  name: string;
  isAvailable(): Promise<boolean>;
  supportsModel(model: string): boolean;
}

/**
 * Modern AIClient - Clean, Simple, Extensible
 * 
 * This class now delegates to the modular LLM system while maintaining
 * backward compatibility with existing code.
 */
export class AIClient {
  private readonly apiKeyManager: APIKeyManager;
  private readonly logger: Logger;
  private readonly configManager: ConfigurationManager;
  private readonly providers: SimpleProvider[];
  private readonly llmManager: LLMClientManager;

  // Simple cache
  private openaiClient?: ChatOpenAI;
  private ollamaClient?: ChatOllama;
  private embeddingClient?: OpenAIEmbeddings;

  // Simple stats
  private stats = {
    requests: 0,
    cacheHits: 0,
  };

  constructor({
    apiKeyManager,
    logger,
  }: {
    apiKeyManager: APIKeyManager;
    logger: Logger;
  }) {
    this.apiKeyManager = apiKeyManager;
    this.logger = logger;
    this.configManager = ConfigurationManager.getInstance(logger);
    this.llmManager = new LLMClientManager(logger, apiKeyManager);

    // Initialize simple providers for backward compatibility
    this.providers = [
      {
        id: 'openai',
        name: 'OpenAI',
        isAvailable: () => this.apiKeyManager.hasOpenAIApiKey(),
        supportsModel: (model: string) => !ConfigUtils.isOllamaModel(model)
      },
      {
        id: 'ollama',
        name: 'Ollama',
        isAvailable: () => Promise.resolve(true),
        supportsModel: (model: string) => ConfigUtils.isOllamaModel(model)
      }
    ];

    this.logger.log("✅ Modern AIClient initialized with modular backend");
  }

  // ============================================================================
  // Core Methods - Backward Compatibility
  // ============================================================================

  async chat({
    prompt,
    maxTokens,
    stop,
    temperature = 0,
  }: {
    prompt: string;
    maxTokens: number;
    stop?: string[];
    temperature?: number;
  }): Promise<AsyncIterable<string>> {
    this.logger.log(["--- Start prompt ---", prompt, "--- End prompt ---"]);
    this.stats.requests++;

    try {
      // Use the new modular system
      const response = await this.llmManager.generateTextStream({
        prompt,
        maxTokens,
        stop,
        temperature
      });

      // Convert to string iterator for backward compatibility
      return this.convertResponseToStringIterator(response);
    } catch (error) {
      this.logger.error(`Chat request failed: ${error}`);
      ErrorHandler.handleError(error, "AIClient.chat");
      throw error;
    }
  }

  async embedding({ input }: { input: string }) {
    this.stats.requests++;

    try {
      // Use the new modular system
      const response = await this.llmManager.generateEmbedding({
        prompt: input
      });

      if (response.type === 'embedding' && response.embedding) {
        return {
          type: "success" as const,
          embedding: response.embedding,
          totalTokenCount: response.usage?.totalTokens
        };
      } else {
        return {
          type: "error" as const,
          errorMessage: response.error || "Failed to generate embedding"
        };
      }
    } catch (error) {
      this.logger.error(`Embedding request failed: ${error}`);
      return {
        type: "error" as const,
        errorMessage: error instanceof Error ? error.message : "Unknown error"
      };
    }
  }

  clearCache(): void {
    this.openaiClient = undefined;
    this.ollamaClient = undefined;
    this.embeddingClient = undefined;
    this.stats.cacheHits = 0;
    this.logger.log("🧹 Cache cleared");
  }

  getCacheStats() {
    return { ...this.stats };
  }

  // ============================================================================
  // Modern API Access
  // ============================================================================

  // Expose the modern LLM manager for advanced use cases
  get modernAPI() {
    return this.llmManager;
  }

  // Helper method to convert modular response to string iterator
  private async *convertResponseToStringIterator(
    response: AsyncIterable<import('./types').StandardizedLLMResponse>
  ): AsyncIterable<string> {
    for await (const chunk of response) {
      if (chunk.type === 'text' && chunk.content) {
        yield chunk.content;
      }
    }
  }
}
