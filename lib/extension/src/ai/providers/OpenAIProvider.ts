/**
 * OpenAI Provider Implementation
 */

import { ChatOpenAI, OpenAIEmbeddings } from "@langchain/openai";
import { ConfigurationManager } from "../../config/ConfigurationManager";
import { Logger } from "../../logger";
import { APIKeyManager } from "../ApiKeyManager";
import { ILLMProvider } from "../interfaces";
import {
  StandardizedLLMRequest,
  StandardizedLLMResponse,
  ProviderCapabilities,
  ProviderConfig,
  ProviderHealth,
  ModelInfo,
  MCPContext,
  RAGContext,
  RAGDocument
} from "../types";

export class OpenAIProvider implements ILLMProvider {
  readonly id = 'openai';
  readonly name = 'OpenAI';
  readonly version = '1.0.0';
  readonly capabilities: ProviderCapabilities = {
    textGeneration: true,
    textCompletion: true,
    imageGeneration: true,
    imageRecognition: true,
    embedding: true,
    toolCalling: true,
    functionCalling: true,
    streaming: true,
    mcpSupport: false,
    ragSupport: true,
    customSettings: ['temperature', 'top_p', 'frequency_penalty', 'presence_penalty']
  };
  
  private config?: ProviderConfig;
  private client?: ChatOpenAI;
  private embeddingClient?: OpenAIEmbeddings;
  
  constructor(
    private apiKeyManager: APIKeyManager,
    private logger: Logger
  ) {}
  
  async initialize(config: ProviderConfig): Promise<void> {
    this.config = config;
    this.logger.log(`🔧 Initialized OpenAI provider with config`);
  }
  
  async dispose(): Promise<void> {
    this.client = undefined;
    this.embeddingClient = undefined;
    this.config = undefined;
  }
  
  async isAvailable(): Promise<boolean> {
    return await this.apiKeyManager.hasOpenAIApiKey();
  }
  
  async getHealth(): Promise<ProviderHealth> {
    const isAvailable = await this.isAvailable();
    return {
      status: isAvailable ? 'healthy' : 'unhealthy',
      lastCheck: new Date(),
      details: isAvailable ? 'API key configured' : 'No API key configured'
    };
  }
  
  async getSupportedModels(): Promise<ModelInfo[]> {
    return [
      {
        id: 'gpt-4o',
        name: 'GPT-4o',
        description: 'Most advanced multimodal model',
        contextLength: 128000,
        capabilities: ['text', 'vision', 'tools'],
        pricing: { input: 0.005, output: 0.015, currency: 'USD' }
      },
      {
        id: 'gpt-4o-mini',
        name: 'GPT-4o Mini',
        description: 'Fast and efficient model',
        contextLength: 128000,
        capabilities: ['text', 'vision', 'tools'],
        pricing: { input: 0.00015, output: 0.0006, currency: 'USD' }
      }
    ];
  }
  
  supportsModel(model: string): boolean {
    return model.startsWith('gpt-') || model.startsWith('o1-');
  }
  
  async generateText(request: StandardizedLLMRequest): Promise<StandardizedLLMResponse> {
    const client = await this.getOrCreateClient(request);
    
    try {
      const response = await client.invoke([
        { role: 'user', content: request.prompt }
      ]);
      
      return {
        type: 'text',
        content: response.content as string,
        finishReason: 'stop',
        model: request.model,
        provider: this.id
      };
    } catch (error) {
      return {
        type: 'error',
        error: error instanceof Error ? error.message : 'Unknown error',
        provider: this.id
      };
    }
  }
  
  async *generateTextStream(request: StandardizedLLMRequest): AsyncIterable<StandardizedLLMResponse> {
    const client = await this.getOrCreateClient(request);
    
    try {
      const stream = await client.stream([
        { role: 'user', content: request.prompt }
      ]);
      
      for await (const chunk of stream) {
        if (chunk.content && typeof chunk.content === 'string') {
          yield {
            type: 'text',
            content: chunk.content,
            model: request.model,
            provider: this.id
          };
        }
      }
    } catch (error) {
      yield {
        type: 'error',
        error: error instanceof Error ? error.message : 'Unknown error',
        provider: this.id
      };
    }
  }
  
  async generateEmbedding(request: StandardizedLLMRequest): Promise<StandardizedLLMResponse> {
    const client = await this.getOrCreateEmbeddingClient();
    
    try {
      const embedding = await client.embedQuery(request.prompt);
      
      return {
        type: 'embedding',
        embedding,
        provider: this.id
      };
    } catch (error) {
      return {
        type: 'error',
        error: error instanceof Error ? error.message : 'Unknown error',
        provider: this.id
      };
    }
  }
  
  async generateImage(_request: StandardizedLLMRequest): Promise<StandardizedLLMResponse> {
    return {
      type: 'error',
      error: 'Image generation not implemented yet',
      provider: this.id
    };
  }
  
  async callTool(_request: StandardizedLLMRequest): Promise<StandardizedLLMResponse> {
    return {
      type: 'error',
      error: 'Tool calling not implemented yet',
      provider: this.id
    };
  }
  
  async callFunction(_request: StandardizedLLMRequest): Promise<StandardizedLLMResponse> {
    return {
      type: 'error',
      error: 'Function calling not implemented yet',
      provider: this.id
    };
  }
  
  async connectMCP(_context: MCPContext): Promise<void> {
    throw new Error('MCP not supported by OpenAI provider');
  }
  
  async disconnectMCP(): Promise<void> {
    // No-op for OpenAI
  }
  
  async setupRAG(_context: RAGContext): Promise<void> {
    // RAG setup would be implemented here
  }
  
  async queryRAG(_query: string): Promise<RAGDocument[]> {
    return [];
  }
  
  private async getOrCreateClient(request: StandardizedLLMRequest): Promise<ChatOpenAI> {
    if (!this.client) {
      const apiKey = await this.apiKeyManager.getOpenAIApiKey();
      const configManager = ConfigurationManager.getInstance(this.logger);
      const config = configManager.getConfiguration();
      
      this.client = new ChatOpenAI({
        apiKey,
        model: request.model || config.model,
        maxTokens: request.maxTokens || config.maxTokens,
        temperature: request.temperature ?? config.temperature,
        stop: request.stop,
        configuration: {
          baseURL: config.openaiBaseUrl,
        },
      });
    }
    
    return this.client;
  }
  
  private async getOrCreateEmbeddingClient(): Promise<OpenAIEmbeddings> {
    if (!this.embeddingClient) {
      const apiKey = await this.apiKeyManager.getOpenAIApiKey();
      const configManager = ConfigurationManager.getInstance(this.logger);
      const config = configManager.getConfiguration();
      
      this.embeddingClient = new OpenAIEmbeddings({
        apiKey,
        model: "text-embedding-ada-002",
        configuration: {
          baseURL: config.openaiBaseUrl,
        },
      });
    }
    
    return this.embeddingClient;
  }
}
