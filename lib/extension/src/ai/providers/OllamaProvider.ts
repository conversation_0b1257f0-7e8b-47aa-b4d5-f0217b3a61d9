/**
 * Ollama Provider Implementation
 */

import { ChatOllama } from "@langchain/ollama";
import { ConfigurationManager, ConfigUtils } from "../../config/ConfigurationManager";
import { Logger } from "../../logger";
import { ILLMProvider } from "../interfaces";
import {
  StandardizedLLMRequest,
  StandardizedLLMResponse,
  ProviderCapabilities,
  ProviderConfig,
  ProviderHealth,
  ModelInfo,
  MCPContext,
  RAGContext,
  RAGDocument
} from "../types";

export class OllamaProvider implements ILLMProvider {
  readonly id = 'ollama';
  readonly name = 'Olla<PERSON>';
  readonly version = '1.0.0';
  readonly capabilities: ProviderCapabilities = {
    textGeneration: true,
    textCompletion: true,
    imageGeneration: false,
    imageRecognition: false,
    embedding: false,
    toolCalling: false,
    functionCalling: false,
    streaming: true,
    mcpSupport: false,
    ragSupport: false,
    customSettings: ['temperature', 'num_predict', 'top_k', 'top_p']
  };
  
  private config?: ProviderConfig;
  private client?: ChatOllama;
  
  constructor(private logger: Logger) {}
  
  async initialize(config: ProviderConfig): Promise<void> {
    this.config = config;
    this.logger.log(`🔧 Initialized Ollama provider with config`);
  }
  
  async dispose(): Promise<void> {
    this.client = undefined;
    this.config = undefined;
  }
  
  async isAvailable(): Promise<boolean> {
    return true; // Assume available for now
  }
  
  async getHealth(): Promise<ProviderHealth> {
    return {
      status: 'healthy',
      lastCheck: new Date(),
      details: 'Ollama provider ready'
    };
  }
  
  async getSupportedModels(): Promise<ModelInfo[]> {
    return [
      {
        id: 'llama3.2',
        name: 'Llama 3.2',
        description: 'Meta Llama 3.2 model',
        contextLength: 8192,
        capabilities: ['text']
      },
      {
        id: 'deepseek-r1:1.5b',
        name: 'DeepSeek R1 1.5B',
        description: 'DeepSeek R1 reasoning model',
        contextLength: 8192,
        capabilities: ['text']
      }
    ];
  }
  
  supportsModel(model: string): boolean {
    return ConfigUtils.isOllamaModel(model);
  }
  
  async generateText(request: StandardizedLLMRequest): Promise<StandardizedLLMResponse> {
    const client = await this.getOrCreateClient(request);
    
    try {
      const response = await client.invoke([
        { role: 'user', content: request.prompt }
      ]);
      
      return {
        type: 'text',
        content: response.content as string,
        finishReason: 'stop',
        model: request.model,
        provider: this.id
      };
    } catch (error) {
      return {
        type: 'error',
        error: error instanceof Error ? error.message : 'Unknown error',
        provider: this.id
      };
    }
  }
  
  async *generateTextStream(request: StandardizedLLMRequest): AsyncIterable<StandardizedLLMResponse> {
    const client = await this.getOrCreateClient(request);
    
    try {
      const stream = await client.stream([
        { role: 'user', content: request.prompt }
      ]);
      
      for await (const chunk of stream) {
        if (chunk.content && typeof chunk.content === 'string') {
          yield {
            type: 'text',
            content: chunk.content,
            model: request.model,
            provider: this.id
          };
        }
      }
    } catch (error) {
      yield {
        type: 'error',
        error: error instanceof Error ? error.message : 'Unknown error',
        provider: this.id
      };
    }
  }
  
  async generateEmbedding(_request: StandardizedLLMRequest): Promise<StandardizedLLMResponse> {
    return {
      type: 'error',
      error: 'Embedding not supported by Ollama provider',
      provider: this.id
    };
  }
  
  async generateImage(_request: StandardizedLLMRequest): Promise<StandardizedLLMResponse> {
    return {
      type: 'error',
      error: 'Image generation not supported by Ollama provider',
      provider: this.id
    };
  }
  
  async callTool(_request: StandardizedLLMRequest): Promise<StandardizedLLMResponse> {
    return {
      type: 'error',
      error: 'Tool calling not supported by Ollama provider',
      provider: this.id
    };
  }
  
  async callFunction(_request: StandardizedLLMRequest): Promise<StandardizedLLMResponse> {
    return {
      type: 'error',
      error: 'Function calling not supported by Ollama provider',
      provider: this.id
    };
  }
  
  async connectMCP(_context: MCPContext): Promise<void> {
    throw new Error('MCP not supported by Ollama provider');
  }
  
  async disconnectMCP(): Promise<void> {
    // No-op for Ollama
  }
  
  async setupRAG(_context: RAGContext): Promise<void> {
    throw new Error('RAG not supported by Ollama provider');
  }
  
  async queryRAG(_query: string): Promise<RAGDocument[]> {
    return [];
  }
  
  private async getOrCreateClient(request: StandardizedLLMRequest): Promise<ChatOllama> {
    if (!this.client) {
      const configManager = ConfigurationManager.getInstance(this.logger);
      const config = configManager.getConfiguration();
      
      this.client = new ChatOllama({
        baseUrl: config.ollamaBaseUrl,
        model: request.model || config.model,
        numPredict: request.maxTokens || config.maxTokens,
        temperature: request.temperature ?? config.temperature,
        stop: request.stop,
      });
    }
    
    return this.client;
  }
}
