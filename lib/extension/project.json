{"root": "lib/extension", "targets": {"compile": {"dependsOn": ["^build"], "executor": "nx:run-commands", "options": {"cwd": "lib/extension", "command": "tsc"}}, "build": {"dependsOn": ["compile"], "executor": "nx:run-commands", "options": {"cwd": "lib/extension", "command": "npx esbuild build/extension.js --external:vscode --bundle --platform=node --target=node18 --format=cjs --minify --sourcemap --tree-shaking=true --outfile=dist/extension.js"}}}}