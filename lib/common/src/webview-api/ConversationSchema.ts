/**
 * ConversationSchema.ts
 * 
 * 定义与对话系统相关的数据结构和类型验证模式。
 * 使用 zod 库进行运行时类型验证，确保数据结构符合预期格式。
 * 这些模式用于在 webview API 中传输和验证对话数据。
 */

import zod from "zod";
import { errorSchema } from "./ErrorSchema";

/**
 * 代码选择区域的模式定义
 * 表示在编辑器中选择的代码片段
 */
export const selectionSchema = zod.object({
  filename: zod.string(), // 选中代码所在的文件名
  startLine: zod.number(), // 选中代码的起始行号
  endLine: zod.number(),   // 选中代码的结束行号
  text: zod.string(),      // 选中的代码文本内容
});

/**
 * 从 selectionSchema 推断出的 TypeScript 类型
 * 表示编辑器中的代码选择
 */
export type Selection = zod.infer<typeof selectionSchema>;

/**
 * 对话消息的模式定义
 * 表示对话中的单条消息，可以是用户或机器人发送的
 */
export const messageSchema = zod.object({
  author: zod.union([zod.literal("user"), zod.literal("bot")]), // 消息作者：用户或机器人
  content: zod.string(), // 消息内容文本
});

/**
 * 从 messageSchema 推断出的 TypeScript 类型
 * 表示对话中的单条消息
 */
export type Message = zod.infer<typeof messageSchema>;

/**
 * 消息交换内容的模式定义
 * 表示一个完整的消息交互会话，包含多条消息和当前会话状态
 */
const messageExchangeContentSchema = zod.object({
  type: zod.literal("messageExchange"), // 内容类型标识符
  messages: zod.array(messageSchema),   // 会话中的所有消息列表
  error: errorSchema.optional(),        // 可选的错误信息
  state: zod.discriminatedUnion("type", [
    // 用户可以回复的状态
    zod.object({
      type: zod.literal("userCanReply"),
      responsePlaceholder: zod.union([zod.string(), zod.undefined()]), // 可选的响应占位文本
    }),
    // 等待机器人回答的状态
    zod.object({
      type: zod.literal("waitingForBotAnswer"),
      botAction: zod.union([zod.string(), zod.undefined()]), // 可选的机器人动作描述
    }),
    // 机器人正在流式传输回答的状态
    zod.object({
      type: zod.literal("botAnswerStreaming"),
      partialAnswer: zod.string(), // 当前已接收的部分回答
    }),
  ]),
});

/**
 * 从 messageExchangeContentSchema 推断出的 TypeScript 类型
 * 表示完整的消息交换会话内容
 */
export type MessageExchangeContent = zod.infer<
  typeof messageExchangeContentSchema
>;

/**
 * 指令精炼内容的模式定义
 * 表示用户可以精炼/修改的指令及其状态
 */
const instructionRefinementContentSchema = zod.object({
  type: zod.literal("instructionRefinement"), // 内容类型标识符
  instruction: zod.string(),                  // 当前指令文本
  error: errorSchema.optional(),              // 可选的错误信息
  state: zod.discriminatedUnion("type", [
    // 用户可以精炼指令的状态
    zod.object({
      type: zod.literal("userCanRefineInstruction"),
      label: zod.union([zod.string(), zod.undefined()]),           // 可选的标签文本
      responseMessage: zod.union([zod.string(), zod.undefined()]), // 可选的响应消息
    }),
    // 等待机器人回答的状态
    zod.object({
      type: zod.literal("waitingForBotAnswer"),
      botAction: zod.union([zod.string(), zod.undefined()]), // 可选的机器人动作描述
    }),
  ]),
});

/**
 * 从 instructionRefinementContentSchema 推断出的 TypeScript 类型
 * 表示指令精炼的内容和状态
 */
export type InstructionRefinementContent = zod.infer<
  typeof instructionRefinementContentSchema
>;

/**
 * 完整对话的模式定义
 * 表示整个对话会话，包含标识符、标题信息和内容
 */
export const conversationSchema = zod.object({
  id: zod.string(),  // 对话的唯一标识符
  header: zod.object({
    title: zod.string(),         // 对话标题
    isTitleMessage: zod.boolean(), // 标题是否为消息内容
    codicon: zod.string(),       // 对话图标的代码名称
  }),
  // 对话内容，可以是消息交换或指令精炼
  content: zod.discriminatedUnion("type", [
    messageExchangeContentSchema,
    instructionRefinementContentSchema,
  ]),
});

/**
 * 从 conversationSchema 推断出的 TypeScript 类型
 * 表示完整的对话会话
 */
export type Conversation = zod.infer<typeof conversationSchema>;