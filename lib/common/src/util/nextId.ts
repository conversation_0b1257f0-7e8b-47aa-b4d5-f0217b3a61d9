import { v4 as uuidv4 } from 'uuid';

export function createNextId({ prefix = "" }: { prefix: string }) {
  let id = 0;
  return () => `${prefix}${id++}`;
}

/**
 * Creates a unique conversation ID with collision detection
 * Format: conversation-{timestamp}-{shortUuid}
 * @param existingIds Set of existing IDs to check for collisions
 * @returns Unique conversation ID
 */
export function createUniqueConversationId(existingIds: Set<string> = new Set()): string {
  let attempts = 0;
  const maxAttempts = 10;

  while (attempts < maxAttempts) {
    const timestamp = Date.now();
    const shortUuid = uuidv4().split('-')[0]; // 8 characters
    const id = `conversation-${timestamp}-${shortUuid}`;

    if (!existingIds.has(id)) {
      return id;
    }
    attempts++;

    // Small delay to ensure different timestamp on retry
    if (attempts < maxAttempts) {
      // Use a synchronous delay for simplicity
      const start = Date.now();
      while (Date.now() - start < 1) {
        // Busy wait for 1ms
      }
    }
  }

  // Fallback: use random string if all attempts failed
  const fallbackId = `conversation-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  console.warn(`Failed to generate unique conversation ID after ${maxAttempts} attempts, using fallback: ${fallbackId}`);
  return fallbackId;
}
