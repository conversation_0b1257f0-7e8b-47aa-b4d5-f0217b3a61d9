{"root": "lib/webview", "targets": {"compile": {"dependsOn": ["^build"], "executor": "nx:run-commands", "options": {"cwd": "lib/webview", "command": "tsc"}}, "build": {"dependsOn": ["compile"], "executor": "nx:run-commands", "options": {"cwd": "lib/webview", "command": "npx esbuild build/webview.js --external:vscode '--define:process.env.NODE_ENV=\"production\"' --bundle --platform=browser --target=es2022 --format=esm --minify --sourcemap --tree-shaking=true --outfile=dist/webview.js"}}, "test": {"executor": "nx:run-commands", "options": {"cwd": "lib/webview", "command": "vitest run"}}, "test-watch": {"executor": "nx:run-commands", "options": {"cwd": "lib/webview", "command": "vitest"}}}}