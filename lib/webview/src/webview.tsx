import { webviewApi } from "@codebeat/common";
import * as React from "react";
import { createRoot } from "react-dom/client";
import { ChatPanelView } from "./panel/ChatPanelView";
import { DiffPanelView } from "./panel/DiffPanelView";
import { sendMessage } from "./vscode/SendMessage";
import * as StateManager from "./vscode/StateManager";

const rootElement = document.getElementById("root");

const panel = document.currentScript?.getAttribute("data-panel-id");
const isStateReloadingEnabled =
  document.currentScript?.getAttribute("data-state-reloading-enabled") ===
  "true";

console.log("CodeBeat webview initializing...", {
  rootElement: !!rootElement,
  panel,
  isStateReloadingEnabled,
  currentScript: !!document.currentScript
});

if (rootElement != undefined) {
  const reactRoot = createRoot(rootElement);

  const render = (panelState?: webviewApi.PanelState) => {
    console.log("CodeBeat webview rendering...", {
      panel,
      panelState: panelState ? { type: panelState.type, hasData: true } : null
    });

    try {
      reactRoot?.render(
        <React.StrictMode>
          {(() => {
            switch (panel) {
              case "chat":
                return (
                  <ChatPanelView
                    sendMessage={sendMessage}
                    panelState={panelState}
                  />
                );
              case "diff":
                return (
                  <DiffPanelView
                    sendMessage={sendMessage}
                    panelState={panelState}
                  />
                );
              default:
                console.warn("Unknown panel type:", panel);
                return <div>Unknown panel type: {panel}</div>;
            }
          })()}
        </React.StrictMode>
      );
      console.log("CodeBeat webview rendered successfully");
    } catch (error) {
      console.error("CodeBeat webview render error:", error);
      // Fallback UI
      reactRoot?.render(
        <div style={{ padding: '16px', color: 'red' }}>
          <h3>Error loading CodeBeat</h3>
          <p>Panel: {panel}</p>
          <p>Error: {error instanceof Error ? error.message : String(error)}</p>
          <button onClick={() => window.location.reload()}>Reload</button>
        </div>
      );
    }
  };

  render(isStateReloadingEnabled ? StateManager.getState() : undefined);
  StateManager.registerUpdateListener(render);
} else {
  console.error("CodeBeat webview: root element not found!");
}
