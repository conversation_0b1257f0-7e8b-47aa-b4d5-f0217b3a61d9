import { webviewApi } from "@codebeat/common";
import { vscodeApi } from "./VsCodeApi";

let state: webviewApi.PanelState = undefined;
let updateListener: ((state: webviewApi.PanelState) => void) | undefined =
  undefined;

// safely load state from VS Code
const loadedState = vscodeApi.getState<unknown>();
console.log("CodeBeat StateManager: Loading initial state", { loadedState });

try {
  state = webviewApi.panelStateSchema.parse(loadedState);
  console.log("CodeBeat StateManager: Initial state parsed successfully", { state });
} catch (error) {
  console.log("CodeBeat StateManager: Failed to parse initial state", {
    loadedState,
    error,
  });
}

const updateState = (newState: webviewApi.PanelState) => {
  console.log("CodeBeat StateManager: Updating state", { newState });
  vscodeApi.setState(newState);
  state = newState;

  if (updateListener != null) {
    console.log("CodeBeat StateManager: Calling update listener");
    updateListener(state);
  } else {
    console.warn("CodeBeat StateManager: No update listener registered");
  }
};

window.addEventListener("message", (rawMessage: unknown) => {
  console.log("CodeBeat StateManager: Received message", { rawMessage });

  try {
    const event = webviewApi.incomingMessageSchema.parse(rawMessage);
    const message = event.data;

    console.log("CodeBeat StateManager: Parsed message", { message });

    if (message.type === "updateState") {
      updateState(message.state);
    }
  } catch (error) {
    console.error("CodeBeat StateManager: Failed to parse message", { rawMessage, error });
  }
});

// exposed as Singleton that is managed outside of React
// (to prevent schema change errors from breaking the UI)

export const registerUpdateListener = (
  listener: (state: webviewApi.PanelState) => void
) => {
  updateListener = listener;
};

export const getState = () => state;
