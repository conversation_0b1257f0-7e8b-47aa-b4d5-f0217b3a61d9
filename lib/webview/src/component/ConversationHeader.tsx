import { webview<PERSON>pi } from "@codebeat/common";
import React from "react";

export const ConversationHeader: React.FC<{
  conversation: webviewApi.Conversation;
  onIconClick?: () => void;
  isExpanded?: boolean;
  onClickExport?: () => void;
  onClickDelete?: () => void;
}> = ({ conversation, onIconClick, isExpanded = false, onClickExport, onClickDelete }) => {
  // Data validation
  if (!conversation?.header) {
    return (
      <div className="header error">
        <i className="codicon codicon-error inline" />
        <span>Invalid conversation data</span>
      </div>
    );
  }

  const { header } = conversation;
  const title = header.title || "Untitled Conversation";
  const codicon = header.codicon || "comment";

  return (
    <div className={`header ${isExpanded ? 'expanded' : 'collapsed'}`}>
      <div className="header-left">
        <i className={`codicon codicon-${codicon} inline`} />
        {header.isTitleMessage ? (
          <span className="message user">{title}</span>
        ) : (
          <span className="title">{title}</span>
        )}
      </div>

      <div className="header-right">
        {onIconClick && isExpanded && (
          <i
            className="codicon codicon-eye inline action-icon"
            title="Insert prompt into editor"
            onClick={onIconClick}
          />
        )}
        {isExpanded && onClickExport && (
          <i
            className="codicon codicon-save inline action-icon"
            title="Export conversation"
            onClick={onClickExport}
          />
        )}
        {isExpanded && onClickDelete && (
          <i
            className="codicon codicon-trash inline action-icon"
            title="Delete conversation"
            onClick={onClickDelete}
          />
        )}
      </div>
    </div>
  );
};
