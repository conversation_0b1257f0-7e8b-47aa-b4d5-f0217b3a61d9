import { webview<PERSON>pi } from "@codebeat/common";
import React from "react";
import { ConversationHeader } from "./ConversationHeader";
import { InstructionRefinementView } from "./InstructionRefinementView";
import { MessageExchangeView } from "./MessageExchangeView";

export const ExpandedConversationView: React.FC<{
  conversation: webviewApi.Conversation;
  onSendMessage: (message: string) => void;
  onClickDismissError: () => void;
  onClickRetry: () => void;
  onClickDelete: () => void;
  onClickExport: () => void;
  onClickInsertPrompt?: () => void;
}> = ({
  conversation,
  onSendMessage,
  onClickDismissError,
  onClickRetry,
  onClickDelete,
  onClickExport,
  onClickInsertPrompt
}) => {
    // Data validation
    if (!conversation?.content) {
      console.error(`Invalid conversation data for ExpandedConversationView: ${conversation?.id}`);
      return (
        <div className="conversation expanded error">
          <div className="header error">
            <i className="codicon codicon-error inline" />
            <span>Invalid conversation data</span>
          </div>
        </div>
      );
    }

    const content = conversation.content;

    return (
      <div className={`conversation expanded`}>
        <ConversationHeader
          conversation={conversation}
          onIconClick={onClickInsertPrompt}
          isExpanded={true}
          onClickExport={onClickExport}
          onClickDelete={onClickDelete}
        />

        {(() => {
          const type = content.type;
          switch (type) {
            case "messageExchange":
              return (
                <MessageExchangeView
                  content={content}
                  onSendMessage={onSendMessage}
                  onClickDismissError={onClickDismissError}
                  onClickRetry={onClickRetry}
                />
              );
            case "instructionRefinement":
              return (
                <InstructionRefinementView
                  content={content}
                  onSendMessage={onSendMessage}
                  onClickDismissError={onClickDismissError}
                  onClickRetry={onClickRetry}
                />
              );
            default: {
              const exhaustiveCheck: never = type;
              throw new Error(`unsupported type: ${exhaustiveCheck}`);
            }
          }
        })()}
      </div>
    );
  };
