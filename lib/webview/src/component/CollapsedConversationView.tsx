import { webviewApi } from "@codebeat/common";
import React from "react";
import { ConversationHeader } from "./ConversationHeader";

export const CollapsedConversationView: React.FC<{
  conversation: webviewApi.Conversation;
  onClick: () => void;
}> = ({ conversation, onClick }) => (
  <div className={`conversation collapsed`} onClick={onClick}>
    <ConversationHeader conversation={conversation} isExpanded={false} />
  </div>
);
