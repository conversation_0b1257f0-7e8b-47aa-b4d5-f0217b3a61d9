import { webviewApi } from "@codebeat/common";
import React from "react";
import { CollapsedConversationView } from "../component/CollapsedConversationView";
import { ExpandedConversationView } from "../component/ExpandedConversationView";
import { SendMessage } from "../vscode/SendMessage";

const StartChatButton: React.FC<{
  onClick: () => void;
}> = ({ onClick }) => (
  <div className="start-chat">
    <button onClick={onClick}>Start new chat</button>
  </div>
);

export const ChatPanelView: React.FC<{
  sendMessage: SendMessage;
  panelState: webviewApi.PanelState;
}> = ({ panelState, sendMessage }) => {
  if (panelState == null) {
    return (
      <StartChatButton onClick={() => sendMessage({ type: "startChat" })} />
    );
  }

  if (panelState.type !== "chat") {
    throw new Error(
      `Invalid panel state '${panelState.type}' (expected 'chat'))`
    );
  }

  // Note: We no longer block the UI when OpenAI API key is missing
  // Users can use Ollama models without an API key, and optionally add OpenAI key later

  if (panelState.conversations.length === 0) {
    return (
      <div>
        <StartChatButton onClick={() => sendMessage({ type: "startChat" })} />
        {!panelState.hasOpenAIApiKey && (
          <div className="api-key-optional-notice" style={{
            marginTop: '16px',
            padding: '12px',
            backgroundColor: '#f3f3f3',
            borderRadius: '4px',
            fontSize: '12px',
            color: '#666'
          }}>
            <p style={{ margin: '0 0 8px 0' }}>
              💡 <strong>Using Ollama models (default)</strong> - No API key required!
            </p>
            <p style={{ margin: '0 0 8px 0' }}>
              Want to use OpenAI models?
              <button
                onClick={() => sendMessage({ type: "enterOpenAIApiKey" })}
                style={{
                  marginLeft: '8px',
                  padding: '2px 8px',
                  fontSize: '11px',
                  backgroundColor: '#007acc',
                  color: 'white',
                  border: 'none',
                  borderRadius: '3px',
                  cursor: 'pointer'
                }}
              >
                Add OpenAI API Key
              </button>
            </p>
          </div>
        )}
      </div>
    );
  }

  // Create a sorted copy without mutating the original array
  const sortedConversations = [...panelState.conversations].reverse();
  const selectedId = panelState.selectedConversationId;

  // Validate selectedId uniqueness
  const matchingConversations = sortedConversations.filter(c => c.id === selectedId);
  if (matchingConversations.length > 1) {
    console.warn(`Multiple conversations with same ID: ${selectedId}`, matchingConversations);
  }

  return (
    <div>
      {sortedConversations.map((conversation, index) => {
        const isSelected = selectedId === conversation.id;
        const isUniqueSelection = isSelected && matchingConversations.length === 1;

        return isUniqueSelection ? (
          <ExpandedConversationView
            key={`${conversation.id}-${index}`}
            conversation={conversation}
            onSendMessage={(message: string) =>
              sendMessage({
                type: "sendMessage",
                data: { id: conversation.id, message },
              })
            }
            onClickRetry={() =>
              sendMessage({
                type: "retry",
                data: { id: conversation.id },
              })
            }
            onClickDismissError={() =>
              sendMessage({
                type: "dismissError",
                data: { id: conversation.id },
              })
            }
            onClickDelete={() =>
              sendMessage({
                type: "deleteConversation",
                data: { id: conversation.id },
              })
            }
            onClickExport={() => {
              sendMessage({
                type: "exportConversation",
                data: { id: conversation.id },
              });
            }}
            onClickInsertPrompt={panelState.surfacePromptForOpenAIPlus ? () => {
              sendMessage({
                type: "insertPromptIntoEditor",
                data: { id: conversation.id },
              })
            } : undefined}
          />
        ) : (
          <CollapsedConversationView
            key={`${conversation.id}-${index}`}
            conversation={conversation}
            onClick={() =>
              sendMessage({
                type: "clickCollapsedConversation",
                data: { id: conversation.id },
              })
            }
          />
        );
      })}
    </div>
  );
};
