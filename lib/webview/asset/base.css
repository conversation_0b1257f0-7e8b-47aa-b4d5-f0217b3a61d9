html {
  box-sizing: border-box;
  font-size: 13px;
  height: 100%;
  margin: 0;
}

body {
  height: 100%;
  background-color: transparent;
  cursor: default;

  margin: 0;
  padding: 0;

  color: var(--vscode-foreground);
  font-size: var(--vscode-font-size);
  font-weight: var(--vscode-font-weight);
  font-family: var(--vscode-font-family);
  background-color: var(--vscode-activity-bar-background);
}

.container {
  padding: 0;
}

.container.top {
  flex-shrink: 0;
  padding: 0 var(--container-padding);
}

.container.content {
  flex-grow: 1;
  overflow: auto;
  min-height: 200px;
}

.container.bottom {
  flex-shrink: 0;
}

*,
*:before,
*:after {
  box-sizing: inherit;
}

h1,
h2,
h3,
h4,
h5,
h6,
p,
ol,
ul {
  margin: 0;
  padding: 0;
  font-weight: normal;
}

img {
  max-width: 100%;
  height: auto;
}

:root {
  --container-padding: 20px;
  --input-padding-vertical: 6px;
  --input-padding-horizontal: 4px;
  --input-margin-vertical: 4px;
  --input-margin-horizontal: 0;
}

#root {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  flex-wrap: nowrap;
  margin: 0;
}

li {
  line-height: 1.5;
}

ol,
ul {
  padding-left: var(--container-padding);
}

body > *,
form > * {
  margin-block-start: var(--input-margin-vertical);
  margin-block-end: var(--input-margin-vertical);
}

*:focus {
  outline-color: var(--vscode-focusBorder) !important;
}

a {
  text-decoration: none;
  color: var(--vscode-textLink-foreground);
  cursor: pointer;
}

a:hover,
a:active {
  text-decoration: underline;
  color: var(--vscode-textLink-activeForeground);
}

code {
  /* Use --vscode-font-size instead of --vscode-editor-font-size
   * so it's visually consistent with the rest of the text.
   */
  font-size: var(--vscode-font-size);
  font-family: var(--vscode-editor-font-family);
}

button {
  margin-top: 8px;
  margin-bottom: 8px;
  border-radius: 2px;
  padding: 4px 8px;

  border: none;
  padding: var(--input-padding-vertical) var(--input-padding-horizontal);
  width: 100%;
  text-align: center;
  outline: 1px solid transparent;
  outline-offset: 2px !important;
  color: var(--vscode-button-foreground);
  background: var(--vscode-button-background);
}

button:hover {
  cursor: pointer;
  background: var(--vscode-button-hoverBackground);
}

button:focus {
  outline-color: var(--vscode-focusBorder);
}

button.secondary {
  color: var(--vscode-button-secondaryForeground);
  background: var(--vscode-button-secondaryBackground);
}

button.secondary:hover {
  background: var(--vscode-button-secondaryHoverBackground);
}

input:not([type="checkbox"]),
textarea {
  display: block;
  width: 100%;
  border: none;
  font-family: var(--vscode-font-family);
  padding: var(--input-padding-vertical) var(--input-padding-horizontal);
  color: var(--vscode-input-foreground);
  outline-color: var(--vscode-input-border);
  background-color: var(--vscode-input-background);
}

input::placeholder,
textarea::placeholder {
  color: var(--vscode-input-placeholderForeground);
}
