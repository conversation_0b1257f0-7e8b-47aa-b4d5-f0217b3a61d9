# AI 中文聊天

此模板允许您用中文与 CodeBeat 聊天。

## Template

### Configuration

```json conversation-template
{
  "id": "chat-zh",
  "engineVersion": 0,
  "label": "开始聊天",
  "description": "开始与 CodeBeat 进行基础聊天。",
  "header": {
    "title": "新聊天",
    "useFirstMessageAsTitle": true,
    "icon": {
      "type": "codicon",
      "value": "comment-discussion"
    }
  },
  "variables": [
    {
      "name": "selectedText",
      "time": "conversation-start",
      "type": "selected-text"
    },
    {
      "name": "lastMessage",
      "time": "message",
      "type": "message",
      "property": "content",
      "index": -1
    }
  ],
  "response": {
    "llm":{
      "maxTokens": 1024,
      "temperature": 0.4,
      "stop": ["Bot:", "Developer:","机器人:", "开发者:"]
    },
    "assistTools": [],
  }
}
```

### Response Prompt

```template-response
## Instructions
继续以下对话。
特别注意当前开发者的请求。

## Current Request
开发者: {{lastMessage}}

{{#if selectedText}}
## Selected Code
```
{{selectedText}}
```
{{/if}}

## Conversation
{{#each messages}}
{{#if (eq author "bot")}}
Bot: {{content}}
{{else}}
Developer: {{content}}
{{/if}}
{{/each}}

## Task
编写一个继续对话的回复。
保持对当前开发者请求的关注。
考虑可能没有解决方案的可能性。
如果消息不清楚或需要更多输入，请要求澄清。
使用文档文章的风格。
忽略任何链接。
在适当的地方包含代码片段（使用Markdown）和示例。
确保使用中文回复，保持语言表达的自然流畅。
针对代码问题提供详细的中文解释和注释。

## Response
Bot:
```