# Find code

## Template

### Configuration

```json conversation-template
{
  "id": "find-code-codebeat",
  "engineVersion": 0,
  "label": "Find code",
  "description": "Find code in the CodeBeat codebase.",
  "header": {
    "title": "Find code",
    "useFirstMessageAsTitle": true,
    "icon": {
      "type": "codicon",
      "value": "search"
    }
  },
  "variables": [
    {
      "name": "lastMessage",
      "time": "message",
      "type": "message",
      "property": "content",
      "index": -1
    }
  ],
  "response": {
    "llm":{
      "maxTokens": 2048,
      "temperature": 0.3,
      "stop": ["Bot:", "Developer:"]
    },
    "ragSetting": {
      "type": "similarity-search",
      "variableName": "searchResults",
      "query": "{{lastMessage}}",
      "source": "embedding-file",
      "file": "codebeat-repository.json",
      "threshold": 0.7,
      "maxResults": 5
    },
    "tools": []
  }
}
```

### Response Prompt

```template-response
## Instructions
Look at the search result and summarize where the code that matches the query is located.

## Query
{{lastMessage}}

## Search Results
{{#each searchResults}}
#### {{file}}
\`\`\`
{{content}}
\`\`\`
{{/each}}

## Task
Summarize where the code that matches the query is located using the search results.

## Response
Bot:
```
