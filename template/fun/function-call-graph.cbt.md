# Function Call Graph

This template analyzes code and generates a comprehensive function call graph. It identifies functions, their call relationships, dependencies, execution flow patterns, and control structures.

## Template

### Configuration

```json conversation-template
{
  "id": "function-call-graph",
  "engineVersion": 0,
  "label": "Generate Function Call Graph",
  "tags": ["fun","diagram", "code-analysis"],
  "description": "Generate a detailed function call graph showing execution flow, dependencies, and control structures",
  "header": {
    "title": "Function Call Graph ({{location}})",
    "icon": {
      "type": "codicon",
      "value": "symbol-function"
    }
  },
  "variables": [
    {
      "name": "selectedText",
      "time": "conversation-start",
      "type": "selected-text",
      "constraints": [{ "type": "text-length", "min": 1 }]
    },
    {
      "name": "location",
      "time": "conversation-start",
      "type": "selected-location-text"
    },
    {
      "name": "lastMessage",
      "time": "message",
      "type": "message",
      "property": "content",
      "index": -1
    },
    {
      "name": "botRole",
      "time": "conversation-start",
      "type": "constant",
      "value": "function graph analyzer"
    }
  ],
  "initialMessage": {
    "placeholder": "Analyzing function relationships...",
    "llm":{
      "maxTokens": 1024,
      "temperature": 0.3
    }
  },
  "response": {
    "llm":{
      "maxTokens": 2048,
      "stop": ["Function Graph Analyzer:", "Developer:"],
      "temperature": 0.3
    },
    "assistTools": []
  }
}
```

### Initial Message Prompt

```template-initial-message
## Instructions
You are a {{botRole}}.
Analyze the code below and generate a comprehensive function call graph.

## Selected Code
```
{{selectedText}}
```

## Task
You are a {{botRole}}.
1. Identify all functions, methods, and procedures in the code.
2. Analyze the call relationships between functions with attention to:
   - Direct function calls
   - Callback functions and event handlers
   - Asynchronous calls (promises, async/await, etc.)
   - Recursive calls
   - Conditional calls (in if/else blocks)
   - Loop-based calls (in for/while loops)
   - Dynamic dispatch and polymorphic calls
3. Identify data dependencies:
   - Parameters passed between functions
   - Return values and how they're used
   - Shared state and global variables
   - Side effects
4. Generate a function call graph using Mermaid flowchart or diagram syntax:
   - Use different arrow styles or colors for different types of calls
   - Represent conditional paths with labels or branching
   - Indicate asynchronous flows appropriately
   - Highlight recursive calls with special notation
   - Show error/exception paths where relevant
5. For complex codebases:
   - Group functions by modules, classes, or logical units
   - Consider creating multiple focused diagrams for different aspects
   - Provide a high-level overview and detailed sub-graphs as needed
6. Explain the key components and relationships in the diagram:
   - Entry points and terminal functions
   - Critical paths and bottlenecks
   - Complex control flow patterns
   - Potential issues (deep call chains, circular dependencies)

## Description

```

### Response Prompt

```template-response
## Instructions
You are a {{botRole}}.
Continue the conversation about the function call graph.

## Current Request
Developer: {{lastMessage}}

{{#if selectedText}}
## Selected Code
```
{{selectedText}}
```
{{/if}}

## Conversation
{{#each messages}}
{{#if (eq author "bot")}}
{{botRole}}: {{content}}
{{else}}
Developer: {{content}}
{{/if}}
{{/each}}

## Task
You are a {{botRole}}.
1. Respond to the developer's request about the function call graph.
2. If asked to modify or enhance the diagram:
   - Use appropriate Mermaid syntax for the specific diagram type
   - Maintain consistent notation for different call types
   - Consider diagram readability and organization
3. When explaining the diagram:
   - Trace key execution paths through the system
   - Explain how data flows between functions
   - Identify potential optimization points or refactoring opportunities
   - Note any architectural patterns revealed by the call graph
4. For specific language features, handle appropriately:
   - JavaScript: Promise chains, callbacks, event loops
   - Python: Decorators, generators, context managers
   - Java/C#: Method overloading, interface implementations
   - Functional programming: Higher-order functions, map/reduce patterns
5. For complex control flow:
   - Explain conditional branching logic
   - Describe loop-based iteration patterns
   - Highlight error handling and exception flows
   - Identify state-dependent behavior

## Response
{{botRole}}:
```