![CodeBeat AI Chat](https://raw.githubusercontent.com/our-aicorp/codebeat-vscode/main/asset/codebeat-header-2.gif)

# CodeBeat: ChatGPT for Visual Studio Code

> &nbsp;
>
> #### AI chat in the Visual Studio Code side bar. CodeBeat can [generate code](#generate-code), [edit code](#edit-code), [explain code](#explain-code), [generate tests](#generate-tests), [find bugs](#find-bugs), [diagnose errors](#diagnose-errors), and more. You can even add [your own conversation templates](#custom-conversations).
>
> &nbsp;

<!-- prettier-ignore-start -->
[![License: MIT](https://img.shields.io/badge/License-MIT-yellow.svg)](https://opensource.org/licenses/MIT)
[![Twitter](https://img.shields.io/twitter/url/https/twitter.com/codebeatai.svg?style=social&label=%20%40codebeatai)](https://twitter.com/codebeatai)
[![Discord](https://discordapp.com/api/guilds/1061938502327091271/widget.png?style=shield)](https://discord.gg/8KN2HmyZmn)<!-- ALL-CONTRIBUTORS-BADGE:START - Do not remove or modify this section -->
[![All Contributors](https://img.shields.io/badge/all_contributors-19-orange.svg?style=flat-square)](#contributors)
<!-- ALL-CONTRIBUTORS-BADGE:END --> 

<!-- prettier-ignore-end -->

## Quick Install

You can install CodeBeat from the

- [Visual Studio Code Marketplace](https://marketplace.visualstudio.com/items?itemName=CodeBeat.codebeat-vscode)
- [Open VSX Registry](https://open-vsx.org/extension/CodeBeat/codebeat-vscode)

CodeBeat requires an OpenAI API key. You can get an OpenAI API key from [platform.openai.com/account/api-keys](https://platform.openai.com/account/api-keys) (you'll need to sign up for an account).

## Features

[AI Chat](#ai-chat) | [Generate Code](#generate-code) | [Edit Code](#edit-code) | [Explain Code](#explain-code) | [Generate Tests](#generate-tests) | [Find Bugs](#find-bugs) | [Diagnose Errors](#diagnose-errors) | [Custom Conversations](#custom-conversations)

### AI Chat

Chat with CodeBeat about your code and software development topics. CodeBeat knows the editor selection at the time of conversation start.

![Chat](https://raw.githubusercontent.com/our-aicorp/codebeat-vscode/main/app/vscode/asset/media/screenshot-start-chat.png)

# Generate Code

Instruct CodeBeat to generate code for you.

![Generate Code](https://raw.githubusercontent.com/our-aicorp/codebeat-vscode/main/app/vscode/asset/media/screenshot-generate-code.gif)

## Edit Code

Change the selected code by instructing CodeBeat to create an edit.

![Edit Code](https://raw.githubusercontent.com/our-aicorp/codebeat-vscode/main/app/vscode/asset/media/screenshot-edit-code.gif)

### Explain Code

Ask CodeBeat to explain the selected code.

![Explain Code](https://raw.githubusercontent.com/our-aicorp/codebeat-vscode/main/app/vscode/asset/media/screenshot-code-explanation.png)

### Generate Tests

Generate test cases for the selected code.

![Generate Tests](https://raw.githubusercontent.com/our-aicorp/codebeat-vscode/main/app/vscode/asset/media/screenshot-generate-test.gif)

## Find Bugs

Find potential defects in your code.

![Find Bugs](https://raw.githubusercontent.com/our-aicorp/codebeat-vscode/main/app/vscode/asset/media/screenshot-find-bugs.png)

### Diagnose Errors

Let CodeBeat identify error causes and suggest fixes to fix compiler and linter errors faster.

![Diagnose Errors](https://raw.githubusercontent.com/our-aicorp/codebeat-vscode/main/app/vscode/asset/media/screenshot-diagnose-errors.gif)

### Custom Conversations

You can define your own conversation templates. See the [CodeBeat Template docs](https://github.com/our-aicorp/codebeat-vscode/blob/main/doc/codebeat-templates.md) for more information.

Here is an example of a [function call graph generator](https://github.com/our-aicorp/codebeat-vscode/blob/main/template/fun/function-call-graph.cbt.md):

![Generate function call graph](https://raw.githubusercontent.com/our-aicorp/codebeat-vscode/main/app/vscode/asset/media/function-call-graph.gif)

## Configuration Options

- **codebeat.syntaxHighlighting.useVisualStudioCodeColors**: Use the Visual Studio Code Theme colors for syntax highlighting in the diff viewer. Might not work with all themes. Default is `false`.

## Built With

- [ModelFusion](https://modelfusion/dev) - AI library
- [Prism.js](https://prismjs.com/) - Syntax highlighting
- [React](https://reactjs.org/) - UI rendering

## Technology Stack

This project uses modern development tools and practices:

### Core Technologies
- **TypeScript 5.8+** - Latest TypeScript with strict type checking
- **Node.js 18+** - Modern JavaScript runtime
- **React 18** - UI framework for webview components
- **ESBuild** - Fast bundling and compilation

### Development Tools
- **ESLint 9** - Code linting with modern configuration
- **Prettier** - Code formatting
- **Vitest** - Fast unit testing framework
- **Nx** - Build system and monorepo management
- **pnpm** - Fast, disk space efficient package manager

### Quality Assurance
- **67+ Test Cases** - Comprehensive test coverage
- **Performance Monitoring** - Built-in performance tracking
- **Error Handling** - Centralized error management
- **Security** - Regular dependency audits and updates

### Performance Metrics
- **Build Time**: ~2.5s (with caching)
- **Test Execution**: <1s per package
- **Bundle Size**:
  - Extension: 308KB
  - Webview: 67KB
  - Common: 98KB

## Contributors

<!-- ALL-CONTRIBUTORS-LIST:START - Do not remove or modify this section -->
<!-- prettier-ignore-start -->
<!-- markdownlint-disable -->
<table>
  <tbody>
    <tr>
      <td align="center" valign="top" width="25%"><a href="http://larsgrammel.de"><img src="https://avatars0.githubusercontent.com/u/205036?v=4?s=100" width="100px;" alt="Lars Grammel"/><br /><sub><b>Lars Grammel</b></sub></a><br /><a href="#ideas-lgrammel" title="Ideas, Planning, & Feedback">🤔</a> <a href="https://github.com/our-aicorp/codebeat-vscode/commits?author=lgrammel" title="Code">💻</a> <a href="https://github.com/our-aicorp/codebeat-vscode/commits?author=lgrammel" title="Documentation">📖</a> <a href="https://github.com/our-aicorp/codebeat-vscode/pulls?q=is%3Apr+reviewed-by%3Algrammel" title="Reviewed Pull Requests">👀</a> <a href="#question-lgrammel" title="Answering Questions">💬</a> <a href="https://github.com/our-aicorp/codebeat-vscode/issues?q=author%3Algrammel" title="Bug reports">🐛</a></td>
      <td align="center" valign="top" width="25%"><a href="http://iainvm.github.io"><img src="https://avatars.githubusercontent.com/u/2806167?v=4?s=100" width="100px;" alt="Iain Majer"/><br /><sub><b>Iain Majer</b></sub></a><br /><a href="https://github.com/our-aicorp/codebeat-vscode/issues?q=author%3Aiainvm" title="Bug reports">🐛</a> <a href="https://github.com/our-aicorp/codebeat-vscode/commits?author=iainvm" title="Code">💻</a></td>
      <td align="center" valign="top" width="25%"><a href="https://nicoespeon.com"><img src="https://avatars0.githubusercontent.com/u/1094774?v=4?s=100" width="100px;" alt="Nicolas Carlo"/><br /><sub><b>Nicolas Carlo</b></sub></a><br /><a href="https://github.com/our-aicorp/codebeat-vscode/commits?author=nicoespeon" title="Code">💻</a> <a href="https://github.com/our-aicorp/codebeat-vscode/commits?author=nicoespeon" title="Documentation">📖</a> <a href="https://github.com/our-aicorp/codebeat-vscode/issues?q=author%3Anicoespeon" title="Bug reports">🐛</a></td>
      <td align="center" valign="top" width="25%"><a href="https://github.com/RatoGBM"><img src="https://avatars.githubusercontent.com/u/80184495?v=4?s=100" width="100px;" alt="RatoGBM"/><br /><sub><b>RatoGBM</b></sub></a><br /><a href="https://github.com/our-aicorp/codebeat-vscode/issues?q=author%3ARatoGBM" title="Bug reports">🐛</a></td>
    </tr>
    <tr>
      <td align="center" valign="top" width="25%"><a href="https://www.lionelokpeicha.dev/"><img src="https://avatars.githubusercontent.com/u/60504466?v=4?s=100" width="100px;" alt="Lionel Okpeicha"/><br /><sub><b>Lionel Okpeicha</b></sub></a><br /><a href="https://github.com/our-aicorp/codebeat-vscode/issues?q=author%3Alohnsonok" title="Bug reports">🐛</a></td>
      <td align="center" valign="top" width="25%"><a href="https://github.com/MercerK"><img src="https://avatars.githubusercontent.com/u/13123338?v=4?s=100" width="100px;" alt="MercerK"/><br /><sub><b>MercerK</b></sub></a><br /><a href="https://github.com/our-aicorp/codebeat-vscode/issues?q=author%3AMercerK" title="Bug reports">🐛</a></td>
      <td align="center" valign="top" width="25%"><a href="https://github.com/lundeen-bryan"><img src="https://avatars.githubusercontent.com/u/13512507?v=4?s=100" width="100px;" alt="Lundeen.Bryan"/><br /><sub><b>Lundeen.Bryan</b></sub></a><br /><a href="#ideas-lundeen-bryan" title="Ideas, Planning, & Feedback">🤔</a></td>
      <td align="center" valign="top" width="25%"><a href="https://github.com/DucoG"><img src="https://avatars.githubusercontent.com/u/67788719?v=4?s=100" width="100px;" alt="DucoG"/><br /><sub><b>DucoG</b></sub></a><br /><a href="#ideas-DucoG" title="Ideas, Planning, & Feedback">🤔</a></td>
    </tr>
    <tr>
      <td align="center" valign="top" width="25%"><a href="https://github.com/sbstn87"><img src="https://avatars.githubusercontent.com/u/37237675?v=4?s=100" width="100px;" alt="sbstn87"/><br /><sub><b>sbstn87</b></sub></a><br /><a href="#ideas-sbstn87" title="Ideas, Planning, & Feedback">🤔</a></td>
      <td align="center" valign="top" width="25%"><a href="https://dev.page/tennox"><img src="https://avatars.githubusercontent.com/u/2084639?v=4?s=100" width="100px;" alt="Manuel"/><br /><sub><b>Manuel</b></sub></a><br /><a href="#ideas-tennox" title="Ideas, Planning, & Feedback">🤔</a></td>
      <td align="center" valign="top" width="25%"><a href="https://github.com/alessandro-newzoo"><img src="https://avatars.githubusercontent.com/u/47320294?v=4?s=100" width="100px;" alt="alessandro-newzoo"/><br /><sub><b>alessandro-newzoo</b></sub></a><br /><a href="#ideas-alessandro-newzoo" title="Ideas, Planning, & Feedback">🤔</a></td>
      <td align="center" valign="top" width="25%"><a href="https://github.com/Void-n-Null"><img src="https://avatars.githubusercontent.com/u/70048414?v=4?s=100" width="100px;" alt="Void&Null"/><br /><sub><b>Void&Null</b></sub></a><br /><a href="#ideas-Void-n-Null" title="Ideas, Planning, & Feedback">🤔</a></td>
    </tr>
    <tr>
      <td align="center" valign="top" width="25%"><a href="https://github.com/WittyDingo"><img src="https://avatars.githubusercontent.com/u/63050074?v=4?s=100" width="100px;" alt="WittyDingo"/><br /><sub><b>WittyDingo</b></sub></a><br /><a href="#ideas-WittyDingo" title="Ideas, Planning, & Feedback">🤔</a></td>
      <td align="center" valign="top" width="25%"><a href="https://github.com/eva-lam"><img src="https://avatars.githubusercontent.com/u/29745387?v=4?s=100" width="100px;" alt="Eva"/><br /><sub><b>Eva</b></sub></a><br /><a href="#ideas-eva-lam" title="Ideas, Planning, & Feedback">🤔</a></td>
      <td align="center" valign="top" width="25%"><a href="https://github.com/AlexeyLavrentev"><img src="https://avatars.githubusercontent.com/u/105936590?v=4?s=100" width="100px;" alt="AlexeyLavrentev"/><br /><sub><b>AlexeyLavrentev</b></sub></a><br /><a href="#ideas-AlexeyLavrentev" title="Ideas, Planning, & Feedback">🤔</a></td>
      <td align="center" valign="top" width="25%"><a href="https://github.com/linshu123"><img src="https://avatars.githubusercontent.com/u/2569559?v=4?s=100" width="100px;" alt="linshu123"/><br /><sub><b>linshu123</b></sub></a><br /><a href="https://github.com/our-aicorp/codebeat-vscode/commits?author=linshu123" title="Documentation">📖</a></td>
    </tr>
    <tr>
      <td align="center" valign="top" width="25%"><a href="https://unquietwiki.com"><img src="https://avatars.githubusercontent.com/u/1007551?v=4?s=100" width="100px;" alt="Michael Adams"/><br /><sub><b>Michael Adams</b></sub></a><br /><a href="https://github.com/our-aicorp/codebeat-vscode/commits?author=unquietwiki" title="Code">💻</a> <a href="https://github.com/our-aicorp/codebeat-vscode/issues?q=author%3Aunquietwiki" title="Bug reports">🐛</a></td>
      <td align="center" valign="top" width="25%"><a href="https://github.com/restlessronin"><img src="https://avatars.githubusercontent.com/u/88921269?v=4?s=100" width="100px;" alt="restlessronin"/><br /><sub><b>restlessronin</b></sub></a><br /><a href="https://github.com/our-aicorp/codebeat-vscode/commits?author=restlessronin" title="Code">💻</a></td>
      <td align="center" valign="top" width="25%"><a href="http://kupczynski.info/"><img src="https://avatars.githubusercontent.com/u/166651?v=4?s=100" width="100px;" alt="Igor Kupczyński"/><br /><sub><b>Igor Kupczyński</b></sub></a><br /><a href="https://github.com/our-aicorp/codebeat-vscode/commits?author=igor-kupczynski" title="Code">💻</a></td>
    </tr>
  </tbody>
</table>

<!-- markdownlint-restore -->
<!-- prettier-ignore-end -->

<!-- ALL-CONTRIBUTORS-LIST:END -->

## Running it locally & Contributing

### [Contributing Guide][contributing]

Read our [contributing guide][contributing] to learn about our development process, how to propose bugfixes and improvements, and how to build and test your changes.

### [Good First Issues][good-first-issues]

To help you get your feet wet and become familiar with our contribution process, we have a list of [good first issues][good-first-issues] that contains things with a relatively limited scope. This is a great place to get started!

<!-- Links -->

[contributing]: https://github.com/our-aicorp/codebeat-vscode/blob/main/CONTRIBUTING.md
[good-first-issues]: https://github.com/our-aicorp/codebeat-vscode/labels/good%20first%20issue
