{"recommendations": ["ms-vscode.vscode-typescript-next", "dbaeumer.vscode-eslint", "esbenp.prettier-vscode", "bradlc.vscode-tailwindcss", "ms-vscode.vscode-json", "redhat.vscode-yaml", "ms-vscode.vscode-markdown", "yzhang.markdown-all-in-one", "streetsidesoftware.code-spell-checker", "ms-vscode.test-adapter-converter", "vitest.explorer", "nrwl.angular-console", "ms-vscode.vscode-github-issue-notebooks", "github.vscode-pull-request-github", "ms-vscode.vscode-github-actions", "ms-vscode.vscode-npm-scripts", "christian-kohler.npm-intellisense", "ms-vscode.vscode-node-debug2", "ms-vscode.vscode-chrome-debug", "formulahendry.auto-rename-tag", "bradlc.vscode-tailwindcss", "ms-vscode.vscode-react-native"], "unwantedRecommendations": ["ms-vscode.vscode-typescript", "hookyqr.beautify", "ms-vscode.vscode-json-languageservice"]}