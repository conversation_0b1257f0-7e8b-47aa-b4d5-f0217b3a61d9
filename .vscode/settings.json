{"typescript.preferences.includePackageJsonAutoImports": "on", "typescript.suggest.autoImports": true, "typescript.updateImportsOnFileMove.enabled": "always", "typescript.preferences.importModuleSpecifier": "relative", "eslint.workingDirectories": ["lib/common", "lib/extension", "lib/webview"], "eslint.validate": ["javascript", "javascriptreact", "typescript", "typescriptreact"], "editor.formatOnSave": true, "editor.codeActionsOnSave": {"source.fixAll.eslint": "explicit", "source.organizeImports": "explicit"}, "editor.rulers": [80, 120], "editor.tabSize": 2, "editor.insertSpaces": true, "files.exclude": {"**/node_modules": true, "**/build": true, "**/dist": true, "**/.nx": true}, "search.exclude": {"**/node_modules": true, "**/build": true, "**/dist": true, "**/.nx": true, "**/benchmark-results.json": true}, "files.watcherExclude": {"**/node_modules/**": true, "**/build/**": true, "**/dist/**": true, "**/.nx/**": true}, "npm.packageManager": "pnpm", "vitest.enable": true, "vitest.commandLine": "pnpm vitest", "files.associations": {"*.cbt.md": "markdown"}, "git.ignoreLimitWarning": true, "cSpell.words": ["chatgpt", "codicon", "openai", "codebeat", "templating", "walkthrough", "walkthroughs", "modelfusion", "prismjs", "vitest", "esbuild"]}