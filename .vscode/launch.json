{"version": "0.2.0", "configurations": [{"name": "Run Extension", "type": "extensionHost", "request": "launch", "runtimeExecutable": "${execPath}", "args": ["--extensionDevelopmentPath=${workspaceFolder}/app/vscode/dev"], "outFiles": ["${workspaceFolder}/app/vscode/dev/extension/dist/**/*.js"], "preLaunchTask": "build-extension"}, {"name": "Debug Tests (Extension)", "type": "node", "request": "launch", "program": "${workspaceFolder}/node_modules/.bin/vitest", "args": ["run", "--reporter=verbose"], "cwd": "${workspaceFolder}/lib/extension", "console": "integratedTerminal", "internalConsoleOptions": "neverOpen", "sourceMaps": true}, {"name": "Debug Tests (Webview)", "type": "node", "request": "launch", "program": "${workspaceFolder}/node_modules/.bin/vitest", "args": ["run", "--reporter=verbose"], "cwd": "${workspaceFolder}/lib/webview", "console": "integratedTerminal", "internalConsoleOptions": "neverOpen", "sourceMaps": true}, {"name": "Debug Tests (Common)", "type": "node", "request": "launch", "program": "${workspaceFolder}/node_modules/.bin/vitest", "args": ["run", "--reporter=verbose"], "cwd": "${workspaceFolder}/lib/common", "console": "integratedTerminal", "internalConsoleOptions": "neverOpen", "sourceMaps": true}, {"name": "Performance Benchmark", "type": "node", "request": "launch", "program": "${workspaceFolder}/scripts/performance-benchmark.js", "cwd": "${workspaceFolder}", "console": "integratedTerminal", "internalConsoleOptions": "neverOpen"}, {"name": "Debug Extension with Logging", "type": "extensionHost", "request": "launch", "runtimeExecutable": "${execPath}", "args": ["--extensionDevelopmentPath=${workspaceFolder}/app/vscode/dev", "--log-level=debug"], "outFiles": ["${workspaceFolder}/app/vscode/dev/extension/dist/**/*.js"], "preLaunchTask": "build-extension", "env": {"VSCODE_DEBUG": "true"}}]}