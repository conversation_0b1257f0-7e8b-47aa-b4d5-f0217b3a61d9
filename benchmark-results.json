{"summary": {"totalBenchmarks": 9, "successful": 8, "failed": 1, "totalDuration": 11421, "averageDuration": 1428, "fastestOperation": {"name": "test-common", "command": "pnpm nx run common:test", "duration": 799, "success": true, "memory": {"heapUsed": 33472, "heapTotal": 0, "external": 1226, "rss": 114688}}, "slowestOperation": {"name": "build-all", "command": "pnpm build-all", "duration": 2518, "success": true, "memory": {"heapUsed": 33344, "heapTotal": 0, "external": 1611, "rss": -393216}}}, "details": {"timestamp": "2025-06-22T11:10:47.865Z", "nodeVersion": "v22.16.0", "platform": "darwin", "arch": "arm64", "benchmarks": {"clean-install": {"name": "clean-install", "command": "pnpm install --frozen-lockfile", "duration": 897, "success": true, "memory": {"heapUsed": 63240, "heapTotal": 0, "external": 1157, "rss": -7225344}}, "typescript-compile-common": {"name": "typescript-compile-common", "command": "pnpm nx run common:compile", "duration": 948, "success": false, "error": "Command failed: pnpm nx run common:compile"}, "typescript-compile-extension": {"name": "typescript-compile-extension", "command": "pnpm nx run extension:compile", "duration": 2311, "success": true, "memory": {"heapUsed": 45536, "heapTotal": 0, "external": 415, "rss": 16384}}, "typescript-compile-webview": {"name": "typescript-compile-webview", "command": "pnpm nx run webview:compile", "duration": 1726, "success": true, "memory": {"heapUsed": 40864, "heapTotal": 0, "external": 409, "rss": -1048576}}, "build-all": {"name": "build-all", "command": "pnpm build-all", "duration": 2518, "success": true, "memory": {"heapUsed": 33344, "heapTotal": 0, "external": 1611, "rss": -393216}}, "test-common": {"name": "test-common", "command": "pnpm nx run common:test", "duration": 799, "success": true, "memory": {"heapUsed": 33472, "heapTotal": 0, "external": 1226, "rss": 114688}}, "test-extension": {"name": "test-extension", "command": "pnpm nx run extension:test", "duration": 947, "success": true, "memory": {"heapUsed": 33240, "heapTotal": 0, "external": 16337, "rss": 49152}}, "test-webview": {"name": "test-webview", "command": "pnpm nx run webview:test", "duration": 848, "success": true, "memory": {"heapUsed": -610600, "heapTotal": 0, "external": -29094, "rss": 2015232}}, "eslint-check": {"name": "eslint-check", "command": "pnpm eslint lib/**/*.{ts,tsx} --max-warnings 0", "duration": 1375, "success": true, "memory": {"heapUsed": 42888, "heapTotal": 0, "external": 0, "rss": -376832}}}, "packageSizes": {"common": {"buildSize": 99934, "buildSizeFormatted": "97.59 KB"}, "extension": {"buildSize": 315124, "buildSizeFormatted": "307.74 KB"}, "webview": {"buildSize": 68269, "buildSizeFormatted": "66.67 KB"}, "nodeModules": {"size": 1817076846, "sizeFormatted": "1.69 GB"}}}}