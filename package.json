{"name": "codebeat-vscode-root", "version": "0.0.0", "license": "MIT", "type": "module", "scripts": {"prepare": "husky install", "build-all": "pnpm nx run-many --target=build", "build-extension": "pnpm nx run vscode:build", "test": "pnpm nx run-many --target=test", "test-watch": "pnpm nx run-many --target=test-watch", "package": "pnpm nx run vscode:package", "deploy:vscode": "pnpm nx run vscode:publish-vscode", "deploy:ovsx": "pnpm nx run vscode:publish-ovsx", "dev": "pnpm build-all && pnpm nx run vscode:dev-setup && code .", "dev:clean": "pnpm build-all && rm -rf app/vscode/dev && pnpm nx run vscode:dev-setup && code .", "clean": "pnpm nx run-many --target=clean && rm -rf node_modules/.cache", "lint": "pnpm eslint lib/**/*.{ts,tsx} --max-warnings 0", "lint:fix": "pnpm eslint lib/**/*.{ts,tsx} --fix", "lint:verbose": "pnpm eslint lib/**/*.{ts,tsx} --format=stylish --max-warnings 0", "format": "pnpm prettier --write lib/**/*.{ts,tsx,json,md}", "type-check": "pnpm nx run-many --target=compile", "audit-fix": "pnpm audit --fix", "benchmark": "node scripts/performance-benchmark.js", "deps:update": "pnpm update --latest", "deps:check": "pnpm outdated"}, "private": true, "devDependencies": {"@eslint/js": "^9.29.0", "@types/node": "^24.0.4", "@typescript-eslint/eslint-plugin": "^8.35.0", "@typescript-eslint/parser": "^8.35.0", "@vscode/vsce": "3.5.0", "esbuild": "0.25.5", "eslint": "^9.29.0", "eslint-config-prettier": "10.1.5", "eslint-plugin-react": "^7.37.5", "husky": "^9.1.7", "lint-staged": "16.1.2", "nx": "21.2.1", "ovsx": "0.10.4", "prettier": "3.6.0", "typescript": "5.8.3", "vitest": "3.2.4"}, "workspaces": ["app/*", "lib/*"], "pnpm": {"overrides": {"ws": ">=8.17.1", "nanoid": ">=3.3.8", "zod": ">=3.22.3", "prismjs": ">=1.30.0"}}}